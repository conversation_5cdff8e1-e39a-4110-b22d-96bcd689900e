# Weasel settings
# encoding: utf-8

config_version: "2024-04-16"

# [app_options]
# 针对特定应用的设置
app_options:
  cmd.exe:
      ascii_mode: true
      ascii_punct: true
  idea64.exe:
      ascii_mode: false
      ascii_punct: true
  Code.exe:
      ascii_mode: false
      ascii_punct: true
  firefox.exe:
    inline_preedit: true # 行内显示预编辑区：规避 <https://github.com/rime/weasel/issues/946>
  # cmd.exe:               # 带 .exe 的进程名：Weasel 15.0 及之前版本须小写; PR #1049 合并后释出的版本大小写不敏感
  #   ascii_mode: true     # 英文模式
  # conhost.exe:
  #   ascii_mode: true
  # windowsterminal.exe:
  #   ascii_mode: true
  # wt.exe:
  #   ascii_mode: true
  # pwsh.exe:
  #   ascii_mode: true
  # powershell.exe:
  #   ascii_mode: true
  # mintty.exe:
  #   ascii_mode: true
  # nvim-qt.exe:
  #   ascii_mode: true
  #   vim_mode: true       # vim 模式, Esc <C-c> <C-[> 切换到 ascii 状态
# [End of <app_options>]

# [global settings]
show_notifications: true                   # 是否显示状态变化的通知：true；false；option_list（方案内的开头 option）
show_notifications_time: 1200              # 通知显示的时间，单位 ms
global_ascii: false                        # 切换为 ascii 模式时，是否影响所有窗口：true；false
# [End of <global settings>]

# [style]
# 字体；候选项、候选窗口的行为、布局及样式
style:
  color_scheme: win11_light      # 默认配色方案

  # 全局字体
  # 格式：字体1:起始码位:结束码位:字重:字形,字体2……，字体会依次 fallback
  # 详细设定请参考 <https://github.com/rime/weasel/wiki/字體設定>
  font_face: "Segoe UI Emoji, Microsoft YaHei, SF Pro, Noto Color Emoji"
  label_font_face: "Microsoft YaHei"       # 标签字体
  comment_font_face: "Microsoft YaHei"     # 注释字体
  font_point: 12                           # 全局字体字号
  label_font_point: 11                     # 标签字体字号，不设定 fallback 到 font_point
  comment_font_point: 11                   # 注释字体字号，不设定 fallback 到 font_point

  inline_preedit: true                     # 行内显示预编辑区：true；false
  preedit_type: composition                # 预编辑区内容：composition（编码）； preview（选中的候选）；preview_all（全部候选）

  fullscreen: false                        # 候选窗口全屏显示：true；false
  horizontal: true                         # 候选项横排：true；false
  vertical_text: false                     # 竖排文本：true；false
  # text_orientation: horizontal           # 文本排列方向，效果和 `vertical_text` 相同：horizontal；vertical
  vertical_text_left_to_right: false       # 竖排方向是否从左到右：true；false
  vertical_text_with_wrap: false           # 文本竖排模式下，自动换行：true；false
  vertical_auto_reverse: false             # 文本竖排模式下，候选窗口位于光标上方时倒序排列：true；false

  label_format: "%s"                       # 标签字符：例如 %s. 效果为 1. 2. 3. ....
  mark_text: ""                           # 标记字符，显示在选中的候选标签前，需要在配色方案中指定颜色；如该项为空字符串 "" 而配色方案中 hilited_mark_color 非透明色，则显示 Windows 11 输入法风格标记
  ascii_tip_follow_cursor: false           # 切换 ASCII 模式时，提示跟随鼠标，而非输入光标
  enhanced_position: true                  # 无法定位候选框时，在窗口左上角显示候选框：true；false
  display_tray_icon: false                 # 托盘显示独立于语言栏的额外图标：true；false
  antialias_mode: default                  # 次像素反锯齿设定：default；force_dword；cleartype；grayscale；aliased
  candidate_abbreviate_length: 30          # 候选项略写，超过此数字则用省略号代替。设置为 0 则不启用此功能
  # mouse_hover_ms: 0                      # ! 已弃用。鼠标悬停选词响应时间（ms），设置为 0 时禁用该功能
  hover_type: semi_hilite                  # 鼠标在候选窗口悬停时：none（无动作）；hilite（选中鼠标下的候选）；semi_hilite（高亮鼠标下的候选）

  paging_on_scroll: true                   # 在候选窗口上滑动滚轮的行为：true（翻页）；false （选中下一个候选）
  click_to_capture: false                  # 鼠标点击候选项，创建截图：true；false

  layout:
    baseline: 0                            # 0 - 100，字号百分比。<https://github.com/rime/weasel/pull/1177>
    linespacing: 0                         # 0 - 100，字号百分比。与 baseline 一同设置可解决字体跳动问题，设置为 0 为禁用
    align_type: bottom                    # 标签、候选文字、注解文字之间的相对对齐方式：top ; center ; bottom
    max_height: 600                        # 候选框最大高度，文本竖排模式下如高度超此尺寸则换列显示候选，设置为 0 不启用此功能
    max_width: 0                           # 候选框最大宽度，horizontal 布局如宽超此尺寸则换行显示候选，设置为 0 不启用此功能
    min_height: 0                          # 候选框最小高度
    min_width: 0                          # 候选框最小宽度
    border_width: 1                       # 边框宽度；又名 border                      
    margin_x: 4                            # 主体元素和候选框的左右边距；为负值时，不显示候选框
    margin_y: 4                            # 主体元素的上下边距；为负值时，不显示候选框
    spacing: 13                            # inline_preedit 为否时，编码区域和候选区域的间距
    candidate_spacing: 12                  # 候选项之间的间距
    line_spacing: 5
    hilite_spacing: 5                      # 候选项和相应标签的间距
    hilite_padding: 5                      # 高亮区域和内部文字的间距，影响高亮区域大小
    # hilite_padding_x: 8                  # 高亮区域和内部文字的左右间距，如无特殊指定则依 hilite_padding 设置
    # hilite_padding_y: 8                  # 高亮区域和内部文字的上下间距，如无特殊指定则依 hilite_padding 设置
    shadow_radius: 0                       # 阴影区域半径，为 0 不显示阴影；需要同时在配色方案中指定非透明的阴影颜色
    shadow_offset_x: 4                     # 阴影左右偏移距离
    shadow_offset_y: 4                     # 阴影上下偏移距离
    corner_radius: 6                       # 候选窗口圆角半径
    round_corner: 6                        # 候选背景色块圆角半径，又名 hilited_corner_radius
    # type: vertical                       # 布局设置，效果和 style 下的设置相同：
                                           # horizontal（横向）；vertical（竖向） ; vertical_text（竖排文本） ; vertical+fullscreen（全屏） ; horizontal+fullscreen（横向全屏）
preset_color_schemes:
  jianchun:
    name: 简纯
    author: amzxyz
    back_color: '0xf2f2f2'
    border_color: '0xCE7539'
    text_color: '0x3c647e'
    hilited_text_color: '0x3c647e'
    hilited_back_color: '0x797954'
    hilited_comment_text_color: '0xffffff'
    hilited_candidate_text_color: '0xffffff'
    hilited_candidate_back_color: '0xCE7539'
    hilited_label_color: '0xdedede'
    candidate_text_color: '0x000000'
    comment_text_color: '0x000000'
    label_color: '0x91897e'

  win11_light:
    name: "Win11浅色 / Win11light"
    text_color: 0x191919
    label_color: 0x191919
    hilited_label_color: 0x191919
    back_color: 0xf9f9f9
    border_color: 0x009e5a00
    hilited_mark_color: 0xc06700
    hilited_candidate_back_color: 0xf0f0f0
    shadow_color: 0x20000000

  win11_dark:
    name: "Win11暗色 / Win11Dark"
    text_color: 0xf9f9f9
    label_color: 0xf9f9f9
    back_color: 0x2C2C2C
    hilited_label_color: 0xf9f9f9
    border_color: 0x002C2C2C
    hilited_mark_color: 0xFFC24C
    hilited_candidate_back_color: 0x383838
    shadow_color: 0x20000000

  mac_light:
    name: "Mac 白"
    text_color: 0x000000
    back_color: 0xffffff
    border_color: 0xe9e9e9
    label_color: 0x999999
    hilited_text_color: 0x000000
    hilited_back_color: 0xffffff
    candidate_text_color: 0x000000
    comment_text_color: 0x999999
    hilited_candidate_text_color: 0xffffff
    hilited_comment_text_color: 0xdddddd
    hilited_candidate_back_color: 16740656
    hilited_label_color: 0xffffff

  wechat:
    name: "微信／Wechat"
    text_color: 0x424242
    label_color: 0x999999
    back_color: 0xFFFFFF
    border_color: 0xFFFFFF
    comment_text_color: 0x999999
    candidate_text_color: 0x3c3c3c
    hilited_comment_text_color: 0xFFFFFF
    hilited_back_color: 0x79af22
    hilited_text_color: 0xFFFFFF
    hilited_label_color: 0xFFFFFF
    hilited_candidate_back_color: 0x79af22
    shadow_color: 0x20000000

  Lumk_light:
    name: "鹿鸣／Lumk light"
    author: "Lumk X <<EMAIL>>"
    back_color: 0xF9F9F9
    border_color: 0xE2E7F5
    candidate_text_color: 0x121212
    comment_text_color: 0x8E8E8E
    hilited_candidate_back_color: 0xECE4FC
    hilited_candidate_label_color: 0xB18FF4
    hilited_candidate_text_color: 0x7A40EC
    hilited_label_color: 0xA483EC
    hilited_mark_color: 0x7A40EC
    label_color: 0x888785
    text_color: 0x8100EB
    shadow_color: 0x20000000