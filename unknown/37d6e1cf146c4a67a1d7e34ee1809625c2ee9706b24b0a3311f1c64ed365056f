name: Nightly Build dicts

on:
  workflow_dispatch: # 手动触发
  push:
    branches:
      - main
    paths:
      - 'cn_dicts/**'

concurrency: # 防止并发冲突
  group: nightly-release
  cancel-in-progress: true

jobs:
  nightly-release:
    runs-on: ubuntu-22.04

    steps:
      # 检出代码
      - name: Checkout repository
        uses: actions/checkout@v4

      # 检查是否需要更新
      - name: Check if cn_dicts or wanxiang-lts-zh-hans.gram has changes
        id: check_changes
        run: |
          if git diff --quiet HEAD HEAD~1 -- cn_dicts wanxiang-lts-zh-hans.gram; then
            echo "SKIP=true" >> $GITHUB_ENV
          else
            echo "SKIP=false" >> $GITHUB_ENV
          fi

      # 条件跳过任务
      - name: Skip if no changes
        if: env.SKIP == 'true'
        run: echo "No changes detected in 'cn_dicts' or 'wanxiang-lts-zh-hans.gram'. Skipping release process."

      # 打包词库文件（但不包含 wanxiang-lts-zh-hans.gram）
      - name: Pack cn_dicts
        if: env.SKIP != 'true'
        run: |
          mkdir -p dist
          echo "Packing cn_dicts..."
          if [ -d "cn_dicts" ]; then
            zip -r dist/cn_dicts.zip cn_dicts
            echo "Packing completed: dist/cn_dicts.zip"
          else
            echo "Error: cn_dicts folder does not exist."
            exit 1
          fi

      # 删除旧的 Release 和 Tag（如果存在）
      - name: Delete existing Nightly Release and Tag
        if: env.SKIP != 'true'
        uses: actions/github-script@v6
        with:
          github-token: ${{ secrets.GITHUB_TOKEN }}
          script: |
            const tag = "dict-nightly";
            try {
              // 检查现有的 Release
              const releases = await github.rest.repos.listReleases({
                owner: context.repo.owner,
                repo: context.repo.repo
              });

              const existingRelease = releases.data.find(r => r.tag_name === tag);
              if (existingRelease) {
                console.log(`Deleting existing Release with ID: ${existingRelease.id}`);
                await github.rest.repos.deleteRelease({
                  owner: context.repo.owner,
                  repo: context.repo.repo,
                  release_id: existingRelease.id
                });
              }

              // 删除现有的 Tag
              console.log(`Deleting tag: ${tag}`);
              await github.rest.git.deleteRef({
                owner: context.repo.owner,
                repo: context.repo.repo,
                ref: `tags/${tag}`
              });
            } catch (error) {
              console.log(`Error deleting Release or Tag: ${error.message}`);
            }

      # 确保删除后等待旧数据清理完成
      - name: Wait for cleanup
        if: env.SKIP != 'true'
        run: sleep 10
      # 安装 Node.js v18（为 action-gh-release 解决 node:fs::FileHandle 问题）
      - name: Setup Node.js 18
        if: env.SKIP != 'true'
        uses: actions/setup-node@v4
        with:
          node-version: '18'
      # 创建新的 Release 并单独上传 `wanxiang-lts-zh-hans.gram`
      - name: Create new Release
        if: env.SKIP != 'true'
        uses: "softprops/action-gh-release@v2"
        with:
          token: ${{ secrets.GITHUB_TOKEN }}
          tag_name: dict-nightly
          name: "每夜构建-词库更新"
          body: |
            - `cn_dicts.zip`：最新的中文词库文件。
            - [wanxiang-lts-zh-hans.gram](https://github.com/amzxyz/RIME-LMDG/releases/download/LTS/wanxiang-lts-zh-hans.gram)：与词库同步更新的语法模型。
          files: |
            dist/cn_dicts.zip
#            wanxiang-lts-zh-hans.gram
          draft: false        # 确保不是草稿
          prerelease: false   # 确保不是预发布版本
          make_latest: true   # 强制标记为最新版本
