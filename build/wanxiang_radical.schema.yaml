__build_info:
  rime_version: 1.13.0
  timestamps:
    default: 1751383420
    default.custom: 1751383723
    wanxiang_radical.custom: 1751383181
    wanxiang_radical.schema: 1751382433
engine:
  filters:
    - uniquifier
  processors:
    - key_binder
    - speller
    - selector
    - navigator
    - express_editor
  segmentors:
    - abc_segmentor
  translators:
    - echo_translator
    - table_translator
key_binder:
  bindings:
    - {accept: KP_0, send: 0, when: composing}
    - {accept: KP_1, send: 1, when: composing}
    - {accept: KP_2, send: 2, when: composing}
    - {accept: KP_3, send: 3, when: composing}
    - {accept: KP_4, send: 4, when: composing}
    - {accept: KP_5, send: 5, when: composing}
    - {accept: KP_6, send: 6, when: composing}
    - {accept: KP_7, send: 7, when: composing}
    - {accept: KP_8, send: 8, when: composing}
    - {accept: KP_9, send: 9, when: composing}
    - {accept: KP_Decimal, send: period, when: composing}
    - {accept: KP_Multiply, send: asterisk, when: composing}
    - {accept: KP_Add, send: plus, when: composing}
    - {accept: KP_Subtract, send: minus, when: composing}
    - {accept: KP_Divide, send: slash, when: composing}
    - {accept: KP_Enter, send: Return, when: composing}
  select_first_character: bracketleft
  select_last_character: bracketright
menu:
  alternative_select_labels:
    - 1
    - 2
    - 3
    - 4
    - 5
    - 6
    - 7
    - 8
    - 9
    - 10
  page_size: 6
schema:
  author: Mirtle
  name: "反查：部件组字"
  schema_id: wanxiang_radical
  version: 1.1.0
set_shuru_schema:
  - "derive/^([jqxy])u(?=^|$|')/$1v/"
  - "derive/'([jqxy])u(?=^|$|')/'$1v/"
  - "derive/^([aoe])([ioun])(?=^|$|')/$1$1$2/"
  - "derive/'([aoe])([ioun])(?=^|$|')/'$1$1$2/"
  - "xform/^([aoe])(ng)?(?=^|$|')/$1$1$2/"
  - "xform/'([aoe])(ng)?(?=^|$|')/'$1$1$2/"
  - "xform/iu(?=^|$|')/<q>/"
  - "xform/(.)ei(?=^|$|')/$1<w>/"
  - "xform/uan(?=^|$|')/<r>/"
  - "xform/[uv]e(?=^|$|')/<t>/"
  - "xform/un(?=^|$|')/<y>/"
  - "xform/^sh/<u>/"
  - "xform/^ch/<i>/"
  - "xform/^zh/<v>/"
  - "xform/'sh/'<u>/"
  - "xform/'ch/'<i>/"
  - "xform/'zh/'<v>/"
  - "xform/uo(?=^|$|')/<o>/"
  - "xform/ie(?=^|$|')/<p>/"
  - "xform/([a-z>])i?ong(?=^|$|')/$1<s>/"
  - "xform/ing(?=^|$|')|uai(?=^|$|')/<k>/"
  - "xform/([a-z>])ai(?=^|$|')/$1<d>/"
  - "xform/([a-z>])en(?=^|$|')/$1<f>/"
  - "xform/([a-z>])eng(?=^|$|')/$1<g>/"
  - "xform/[iu]ang(?=^|$|')/<l>/"
  - "xform/([a-z>])ang(?=^|$|')/$1<h>/"
  - "xform/ian(?=^|$|')/<m>/"
  - "xform/([a-z>])an(?=^|$|')/$1<j>/"
  - "xform/([a-z>])ou(?=^|$|')/$1<z>/"
  - "xform/[iu]a(?=^|$|')/<x>/"
  - "xform/iao(?=^|$|')/<n>/"
  - "xform/([a-z>])ao(?=^|$|')/$1<c>/"
  - "xform/ui(?=^|$|')/<v>/"
  - "xform/in(?=^|$|')/<b>/"
  - "xform/'|<|>//"
speller:
  algebra:
    - "derive/^([jqxy])u(?=^|$|')/$1v/"
    - "derive/'([jqxy])u(?=^|$|')/'$1v/"
    - "derive/^([aoe])([ioun])(?=^|$|')/$1$1$2/"
    - "derive/'([aoe])([ioun])(?=^|$|')/'$1$1$2/"
    - "xform/^([aoe])(ng)?(?=^|$|')/$1$1$2/"
    - "xform/'([aoe])(ng)?(?=^|$|')/'$1$1$2/"
    - "xform/iu(?=^|$|')/<q>/"
    - "xform/(.)ei(?=^|$|')/$1<w>/"
    - "xform/uan(?=^|$|')/<r>/"
    - "xform/[uv]e(?=^|$|')/<t>/"
    - "xform/un(?=^|$|')/<y>/"
    - "xform/^sh/<u>/"
    - "xform/^ch/<i>/"
    - "xform/^zh/<v>/"
    - "xform/'sh/'<u>/"
    - "xform/'ch/'<i>/"
    - "xform/'zh/'<v>/"
    - "xform/uo(?=^|$|')/<o>/"
    - "xform/ie(?=^|$|')/<p>/"
    - "xform/([a-z>])i?ong(?=^|$|')/$1<s>/"
    - "xform/ing(?=^|$|')|uai(?=^|$|')/<k>/"
    - "xform/([a-z>])ai(?=^|$|')/$1<d>/"
    - "xform/([a-z>])en(?=^|$|')/$1<f>/"
    - "xform/([a-z>])eng(?=^|$|')/$1<g>/"
    - "xform/[iu]ang(?=^|$|')/<l>/"
    - "xform/([a-z>])ang(?=^|$|')/$1<h>/"
    - "xform/ian(?=^|$|')/<m>/"
    - "xform/([a-z>])an(?=^|$|')/$1<j>/"
    - "xform/([a-z>])ou(?=^|$|')/$1<z>/"
    - "xform/[iu]a(?=^|$|')/<x>/"
    - "xform/iao(?=^|$|')/<n>/"
    - "xform/([a-z>])ao(?=^|$|')/$1<c>/"
    - "xform/ui(?=^|$|')/<v>/"
    - "xform/in(?=^|$|')/<b>/"
    - "xform/'|<|>//"
  alphabet: "abcdefghijklmnopqrstuvwxyz;"
  delimiter: " '"
translator:
  dictionary: wanxiang_radical
  enable_user_dict: false
"全拼":
  - "xform/'//"
  - "derive/^([nl])ue$/$1ve/"
  - "derive/'([nl])ue$/'$1ve/"
  - "derive/^([jqxy])u/$1v/"
  - "derive/'([jqxy])u/'$1v/"
"小鹤双拼":
  - "derive/^([jqxy])u(?=^|$|')/$1v/"
  - "derive/'([jqxy])u(?=^|$|')/'$1v/"
  - "derive/^([aoe])([ioun])(?=^|$|')/$1$1$2/"
  - "derive/'([aoe])([ioun])(?=^|$|')/'$1$1$2/"
  - "xform/^([aoe])(ng)?(?=^|$|')/$1$1$2/"
  - "xform/'([aoe])(ng)?(?=^|$|')/'$1$1$2/"
  - "xform/iu(?=^|$|')/<q>/"
  - "xform/(.)ei(?=^|$|')/$1<w>/"
  - "xform/uan(?=^|$|')/<r>/"
  - "xform/[uv]e(?=^|$|')/<t>/"
  - "xform/un(?=^|$|')/<y>/"
  - "xform/^sh/<u>/"
  - "xform/^ch/<i>/"
  - "xform/^zh/<v>/"
  - "xform/'sh/'<u>/"
  - "xform/'ch/'<i>/"
  - "xform/'zh/'<v>/"
  - "xform/uo(?=^|$|')/<o>/"
  - "xform/ie(?=^|$|')/<p>/"
  - "xform/([a-z>])i?ong(?=^|$|')/$1<s>/"
  - "xform/ing(?=^|$|')|uai(?=^|$|')/<k>/"
  - "xform/([a-z>])ai(?=^|$|')/$1<d>/"
  - "xform/([a-z>])en(?=^|$|')/$1<f>/"
  - "xform/([a-z>])eng(?=^|$|')/$1<g>/"
  - "xform/[iu]ang(?=^|$|')/<l>/"
  - "xform/([a-z>])ang(?=^|$|')/$1<h>/"
  - "xform/ian(?=^|$|')/<m>/"
  - "xform/([a-z>])an(?=^|$|')/$1<j>/"
  - "xform/([a-z>])ou(?=^|$|')/$1<z>/"
  - "xform/[iu]a(?=^|$|')/<x>/"
  - "xform/iao(?=^|$|')/<n>/"
  - "xform/([a-z>])ao(?=^|$|')/$1<c>/"
  - "xform/ui(?=^|$|')/<v>/"
  - "xform/in(?=^|$|')/<b>/"
  - "xform/'|<|>//"
"微软双拼":
  - "derive/^([jqxy])u(?=^|$|')/$1v/"
  - "derive/'([jqxy])u(?=^|$|')/'$1v/"
  - "derive/^([aoe].*)(?=^|$|')/o$1/"
  - "derive/'([aoe].*)(?=^|$|')/'o$1/"
  - "xform/^([ae])(.*)(?=^|$|')/$1$1$2/"
  - "xform/'([ae])(.*)(?=^|$|')/'$1$1$2/"
  - "xform/iu(?=^|$|')/<q>/"
  - "xform/[iu]a(?=^|$|')/<w>/"
  - "xform/er(?=^|$|')|[uv]an(?=^|$|')/<r>/"
  - "xform/[uv]e(?=^|$|')/<t>/"
  - "xform/v(?=^|$|')|uai(?=^|$|')/<y>/"
  - "xform/^sh/<u>/"
  - "xform/^ch/<i>/"
  - "xform/^zh/<v>/"
  - "xform/'sh/'<u>/"
  - "xform/'ch/'<i>/"
  - "xform/'zh/'<v>/"
  - "xform/uo(?=^|$|')/<o>/"
  - "xform/[uv]n(?=^|$|')/<p>/"
  - "xform/([a-z>])i?ong(?=^|$|')/$1<s>/"
  - "xform/[iu]ang(?=^|$|')/<d>/"
  - "xform/([a-z>])en(?=^|$|')/$1<f>/"
  - "xform/([a-z>])eng(?=^|$|')/$1<g>/"
  - "xform/([a-z>])ang(?=^|$|')/$1<h>/"
  - "xform/ian(?=^|$|')/<m>/"
  - "xform/([a-z>])an(?=^|$|')/$1<j>/"
  - "xform/iao(?=^|$|')/<c>/"
  - "xform/([a-z>])ao(?=^|$|')/$1<k>/"
  - "xform/([a-z>])ai(?=^|$|')/$1<l>/"
  - "xform/([a-z>])ei(?=^|$|')/$1<z>/"
  - "xform/ie(?=^|$|')/<x>/"
  - "xform/ui(?=^|$|')/<v>/"
  - "derive/<t>(?=^|$|')/<v>/"
  - "xform/([a-z>])ou(?=^|$|')/$1<b>/"
  - "xform/in(?=^|$|')/<n>/"
  - "xform/ing(?=^|$|')/;/"
  - "xform/'|<|>//"
"拼音加加":
  - "derive/^([jqxy])u(?=^|$|')/$1v/"
  - "derive/'([jqxy])u(?=^|$|')/'$1v/"
  - "derive/^([aoe])([ioun])(?=^|$|')/$1$1$2/"
  - "derive/'([aoe])([ioun])(?=^|$|')/'$1$1$2/"
  - "xform/^([aoe])(ng)?(?=^|$|')/$1$1$2/"
  - "xform/'([aoe])(ng)?(?=^|$|')/'$1$1$2/"
  - "xform/iu(?=^|$|')/<n>/"
  - "xform/[iu]a(?=^|$|')/<b>/Add commentMore actions"
  - "xform/[uv]an(?=^|$|')/<c>/"
  - "xform/[uv]e(?=^|$|')/<x>/"
  - "xform/ing(?=^|$|')|er(?=^|$|')/<q>/"
  - "xform/^sh/<i>/"
  - "xform/^ch/<u>/"
  - "xform/^zh/<v>/"
  - "xform/'sh/'<i>/"
  - "xform/'ch/'<u>/"
  - "xform/'zh/'<v>/"
  - "xform/uo(?=^|$|')/<o>/"
  - "xform/[uv]n(?=^|$|')/<z>/"
  - "xform/([a-z>])i?ong(?=^|$|')/$1<y>/"
  - "xform/[iu]ang(?=^|$|')/<h>/"
  - "xform/([a-z>])en(?=^|$|')/$1<r>/"
  - "xform/([a-z>])eng(?=^|$|')/$1<t>/"
  - "xform/([a-z>])ang(?=^|$|')/$1<g>/"
  - "xform/ian(?=^|$|')/<j>/"
  - "xform/([a-z>])an(?=^|$|')/$1<f>/"
  - "xform/iao(?=^|$|')/<k>/"
  - "xform/([a-z>])ao(?=^|$|')/$1<d>/"
  - "xform/([a-z>])ai(?=^|$|')/$1<s>/"
  - "xform/([a-z>])ei(?=^|$|')/$1<w>/"
  - "xform/ie(?=^|$|')/<m>/"
  - "xform/ui(?=^|$|')/<v>/"
  - "xform/([a-z>])ou(?=^|$|')/$1<p>/"
  - "xform/in(?=^|$|')/<l>/"
  - "xform/'|<|>//"
"搜狗双拼":
  - "derive/^([jqxy])u(?=^|$|')/$1v/"
  - "derive/'([jqxy])u(?=^|$|')/'$1v/"
  - "derive/^([aoe].*)(?=^|$|')/o$1/"
  - "derive/'([aoe].*)(?=^|$|')/'o$1/"
  - "xform/^([ae])(.*)(?=^|$|')/$1$1$2/"
  - "xform/'([ae])(.*)(?=^|$|')/'$1$1$2/"
  - "xform/iu(?=^|$|')/<q>/"
  - "xform/[iu]a(?=^|$|')/<w>/"
  - "xform/er(?=^|$|')|[uv]an(?=^|$|')/<r>/"
  - "xform/[uv]e(?=^|$|')/<t>/"
  - "xform/v(?=^|$|')|uai(?=^|$|')/<y>/"
  - "xform/^sh/<u>/"
  - "xform/^ch/<i>/"
  - "xform/^zh/<v>/"
  - "xform/'sh/'<u>/"
  - "xform/'ch/'<i>/"
  - "xform/'zh/'<v>/"
  - "xform/uo(?=^|$|')/<o>/"
  - "xform/[uv]n(?=^|$|')/<p>/"
  - "xform/([a-z>])i?ong(?=^|$|')/$1<s>/"
  - "xform/[iu]ang(?=^|$|')/<d>/"
  - "xform/([a-z>])en(?=^|$|')/$1<f>/"
  - "xform/([a-z>])eng(?=^|$|')/$1<g>/"
  - "xform/([a-z>])ang(?=^|$|')/$1<h>/"
  - "xform/ian(?=^|$|')/<m>/"
  - "xform/([a-z>])an(?=^|$|')/$1<j>/"
  - "xform/iao(?=^|$|')/<c>/"
  - "xform/([a-z>])ao(?=^|$|')/$1<k>/"
  - "xform/([a-z>])ai(?=^|$|')/$1<l>/"
  - "xform/([a-z>])ei(?=^|$|')/$1<z>/"
  - "xform/ie(?=^|$|')/<x>/"
  - "xform/ui(?=^|$|')/<v>/"
  - "xform/([a-z>])ou(?=^|$|')/$1<b>/"
  - "xform/in(?=^|$|')/<n>/"
  - "xform/ing(?=^|$|')/;/"
  - "xform/'|<|>//"
"智能ABC":
  - "xform/^zh/<a>/"
  - "xform/^ch/<e>/"
  - "xform/^sh/<v>/"
  - "xform/'zh/'<a>/"
  - "xform/'ch/'<e>/"
  - "xform/'sh/'<v>/"
  - "xform/^([aoe].*)(?=^|$|')/<o>$1/"
  - "xform/'([aoe].*)(?=^|$|')/'<o>$1/"
  - "xform/ei(?=^|$|')/<q>/"
  - "xform/ian(?=^|$|')/<w>/"
  - "xform/er(?=^|$|')|iu(?=^|$|')/<r>/"
  - "xform/[iu]ang(?=^|$|')/<t>/"
  - "xform/ing(?=^|$|')/<y>/"
  - "xform/uo(?=^|$|')/<o>/"
  - "xform/uan(?=^|$|')/<p>/"
  - "xform/([a-z>])i?ong(?=^|$|')/$1<s>/"
  - "xform/[iu]a(?=^|$|')/<d>/"
  - "xform/en(?=^|$|')/<f>/"
  - "xform/eng(?=^|$|')/<g>/"
  - "xform/ang(?=^|$|')/<h>/"
  - "xform/an(?=^|$|')/<j>/"
  - "xform/iao(?=^|$|')/<z>/"
  - "xform/ao(?=^|$|')/<k>/"
  - "xform/in(?=^|$|')|uai(?=^|$|')/<c>/"
  - "xform/ai(?=^|$|')/<l>/"
  - "xform/ie(?=^|$|')/<x>/"
  - "xform/ou(?=^|$|')/<b>/"
  - "xform/un(?=^|$|')/<n>/"
  - "xform/[uv]e(?=^|$|')|ui(?=^|$|')/<m>/"
  - "xform/'|<|>//"
"紫光双拼":
  - "derive/^([jqxy])u(?=^|$|')/$1v/"
  - "derive/'([jqxy])u(?=^|$|')/'$1v/"
  - "xform/'([aoe].*)(?=^|$|')/'<o>$1/"
  - "xform/^([aoe].*)(?=^|$|')/<o>$1/"
  - "xform/en(?=^|$|')/<w>/"
  - "xform/eng(?=^|$|')/<t>/"
  - "xform/in(?=^|$|')|uai(?=^|$|')/<y>/"
  - "xform/^zh/<u>/"
  - "xform/^sh/<i>/"
  - "xform/'zh/'<u>/"
  - "xform/'sh/'<i>/"
  - "xform/uo(?=^|$|')/<o>/"
  - "xform/ai(?=^|$|')/<p>/"
  - "xform/^ch/<a>/"
  - "xform/'ch/'<a>/"
  - "xform/[iu]ang(?=^|$|')/<g>/"
  - "xform/ang(?=^|$|')/<s>/"
  - "xform/ie(?=^|$|')/<d>/"
  - "xform/ian(?=^|$|')/<f>/"
  - "xform/([a-z>])i?ong(?=^|$|')/$1<h>/"
  - "xform/er(?=^|$|')|iu(?=^|$|')/<j>/"
  - "xform/ei(?=^|$|')/<k>/"
  - "xform/uan(?=^|$|')/<l>/"
  - "xform/ing(?=^|$|')/;/"
  - "xform/ou(?=^|$|')/<z>/"
  - "xform/[iu]a(?=^|$|')/<x>/"
  - "xform/iao(?=^|$|')/<b>/"
  - "xform/ue(?=^|$|')|ui(?=^|$|')|ve(?=^|$|')/<n>/"
  - "xform/un(?=^|$|')/<m>/"
  - "xform/ao(?=^|$|')/<q>/"
  - "xform/an(?=^|$|')/<r>/"
  - "xform/'|<|>//"
"自然码":
  - "derive/^([jqxy])u(?=^|$|')/$1v/"
  - "derive/'([jqxy])u(?=^|$|')/'$1v/"
  - "derive/^([aoe])([ioun])(?=^|$|')/$1$1$2/"
  - "derive/'([aoe])([ioun])(?=^|$|')/'$1$1$2/"
  - "xform/^([aoe])(ng)?(?=^|$|')/$1$1$2/"
  - "xform/'([aoe])(ng)?(?=^|$|')/'$1$1$2/"
  - "xform/iu(?=^|$|')/<q>/"
  - "xform/[iu]a(?=^|$|')/<w>/"
  - "xform/[uv]an(?=^|$|')/<r>/"
  - "xform/[uv]e(?=^|$|')/<t>/"
  - "xform/ing(?=^|$|')|uai(?=^|$|')/<y>/"
  - "xform/^sh/<u>/"
  - "xform/^ch/<i>/"
  - "xform/^zh/<v>/"
  - "xform/'sh/'<u>/"
  - "xform/'ch/'<i>/"
  - "xform/'zh/'<v>/"
  - "xform/uo(?=^|$|')/<o>/"
  - "xform/[uv]n(?=^|$|')/<p>/"
  - "xform/([a-z>])i?ong(?=^|$|')/$1<s>/"
  - "xform/[iu]ang(?=^|$|')/<d>/"
  - "xform/([a-z>])en(?=^|$|')/$1<f>/"
  - "xform/([a-z>])eng(?=^|$|')/$1<g>/"
  - "xform/([a-z>])ang(?=^|$|')/$1<h>/"
  - "xform/ian(?=^|$|')/<m>/"
  - "xform/([a-z>])an(?=^|$|')/$1<j>/"
  - "xform/iao(?=^|$|')/<c>/"
  - "xform/([a-z>])ao(?=^|$|')/$1<k>/"
  - "xform/([a-z>])ai(?=^|$|')/$1<l>/"
  - "xform/([a-z>])ei(?=^|$|')/$1<z>/"
  - "xform/ie(?=^|$|')/<x>/"
  - "xform/ui(?=^|$|')/<v>/"
  - "xform/([a-z>])ou(?=^|$|')/$1<b>/"
  - "xform/in(?=^|$|')/<n>/"
  - "xform/'|<|>//"