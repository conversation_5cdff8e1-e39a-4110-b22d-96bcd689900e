__build_info:
  rime_version: 1.13.0
  timestamps:
    default: 1751372775
    default.custom: 0
    wanxiang_stroke.custom: 0
    wanxiang_stroke.schema: 1751372775
engine:
  processors:
    - ascii_composer
    - recognizer
    - key_binder
    - speller
    - punctuator
    - selector
    - navigator
    - express_editor
  segmentors:
    - abc_segmentor
  translators:
    - punct_translator
    - table_translator
key_binder:
  bindings:
    - {accept: KP_0, send: 0, when: composing}
    - {accept: KP_1, send: 1, when: composing}
    - {accept: KP_2, send: 2, when: composing}
    - {accept: KP_3, send: 3, when: composing}
    - {accept: KP_4, send: 4, when: composing}
    - {accept: KP_5, send: 5, when: composing}
    - {accept: KP_6, send: 6, when: composing}
    - {accept: KP_7, send: 7, when: composing}
    - {accept: KP_8, send: 8, when: composing}
    - {accept: KP_9, send: 9, when: composing}
    - {accept: KP_Decimal, send: period, when: composing}
    - {accept: KP_Multiply, send: asterisk, when: composing}
    - {accept: KP_Add, send: plus, when: composing}
    - {accept: KP_Subtract, send: minus, when: composing}
    - {accept: KP_Divide, send: slash, when: composing}
    - {accept: KP_Enter, send: Return, when: composing}
  select_first_character: bracketleft
  select_last_character: bracketright
menu:
  alternative_select_labels:
    - 1
    - 2
    - 3
    - 4
    - 5
    - 6
    - 7
    - 8
    - 9
    - 10
  page_size: 6
schema:
  author:
    - amzxyz
  description: |
    五笔画
    h,s,p,n,z 代表橫、竖、撇、捺、折

  name: "反查：五笔画"
  schema_id: wanxiang_stroke
  version: 0.5
speller:
  alphabet: abcdefghijklmnopqrstuvwxyz
  delimiter: " '"
translator:
  dictionary: wanxiang_stroke
  enable_user_dict: false