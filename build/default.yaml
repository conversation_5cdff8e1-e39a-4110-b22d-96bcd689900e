__build_info:
  rime_version: 1.13.0
  timestamps:
    default: 1751383420
    default.custom: 1751383723
ascii_composer:
  good_old_caps_lock: true
  switch_key:
    Caps_Lock: commit_code
    Control_L: noop
    Control_R: noop
    Shift_L: noop
    Shift_R: commit_code
config_version: "2024-08-05"
key_binder:
  bindings:
    - {accept: KP_0, send: 0, when: composing}
    - {accept: KP_1, send: 1, when: composing}
    - {accept: KP_2, send: 2, when: composing}
    - {accept: KP_3, send: 3, when: composing}
    - {accept: KP_4, send: 4, when: composing}
    - {accept: KP_5, send: 5, when: composing}
    - {accept: KP_6, send: 6, when: composing}
    - {accept: KP_7, send: 7, when: composing}
    - {accept: KP_8, send: 8, when: composing}
    - {accept: KP_9, send: 9, when: composing}
    - {accept: KP_Decimal, send: period, when: composing}
    - {accept: KP_Multiply, send: asterisk, when: composing}
    - {accept: KP_Add, send: plus, when: composing}
    - {accept: KP_Subtract, send: minus, when: composing}
    - {accept: KP_Divide, send: slash, when: composing}
    - {accept: KP_Enter, send: Return, when: composing}
  select_first_character: bracketleft
  select_last_character: bracketright
menu:
  alternative_select_labels:
    - 1
    - 2
    - 3
    - 4
    - 5
    - 6
    - 7
    - 8
    - 9
    - 10
  page_size: 6
recognizer:
  patterns:
    colon: "^[A-Za-z]+:.*"
    email: "^[A-Za-z][-_.0-9A-Za-z]*@.*$"
    underscore: "^[A-Za-z]+_.*"
    url: "^(www[.]|https?:|ftp[.:]|mailto:|file:).*$|^[a-z]+[.].+$"
    url_2: "^[A-Za-z]+[.].*"
schema_list:
  - schema: wanxiang
switcher:
  abbreviate_options: true
  caption: "「方案选单」"
  fold_options: true
  hotkeys:
    - "Control+grave"
  option_list_separator: " / "
  save_options:
    - ascii_punct
    - traditionalization
    - s2t
    - s2hk
    - s2tw
    - emoji
    - full_shape
    - prediction
    - super_tips
    - charset_filter
    - chaifen_switch
    - tone_display
    - fuzhu_hint
    - tone_hint
    - chinese_english
    - search_single_char