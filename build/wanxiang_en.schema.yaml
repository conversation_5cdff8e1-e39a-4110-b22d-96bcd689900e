__build_info:
  rime_version: 1.13.0
  timestamps:
    default: 1751372775
    default.custom: 0
    wanxiang_en.custom: 0
    wanxiang_en.schema: 1751382439
algebra_common:
  - "derive/1([4-7|9])/$1teen/"
  - "derive/11/eleven/"
  - "derive/12/twelve/"
  - "derive/13/thirteen/"
  - "derive/15/fifteen/"
  - "derive/18/eighteen/"
  - "derive/0/o/"
  - "derive/0/O/"
  - "derive/0/zero/"
  - "derive/1/one/"
  - "derive/10/ten/"
  - "derive/2/to/"
  - "derive/2/two/"
  - "derive/3/three/"
  - "derive/4/for/"
  - "derive/4/four/"
  - "derive/5/five/"
  - "derive/6/six/"
  - "derive/7/seven/"
  - "derive/8/eight/"
  - "derive/9/nine/"
  - "derive/\\+/plus/"
  - "derive/\\./dot/"
  - "derive/@/at/"
  - "derive/-/hyphen/"
  - "derive/#/hash/"
  - "derive/#/number/"
  - "derive/#/sharp/"
  - "derive/♯/sharp/"
  - "derive / slash"
  - "derive/&/and/"
  - "derive/%/percent/"
  - "derive/[.]//"
  - "derive/[+]//"
  - "derive/[@]//"
  - "derive/[-]//"
  - "derive/[_]//"
  - "derive/[^a-zA-Z0-9]//"
  - "erase/^[^a-zA-Z0-9].+$/"
  - "derive/^.+$/\\L$0/"
  - "derive/^.+$/\\U$0/"
  - "derive/^./\\U$0/"
  - "derive/^([a-z]{2})/\\U$1/"
  - "derive/^([a-z]{3})/\\U$1/"
  - "derive/^([a-z]{4})/\\U$1/"
  - "derive/^([a-z]{5})/\\U$1/"
  - "derive/^([a-z]{6})/\\U$1/"
  - "derive/^([a-z]{7})/\\U$1/"
  - "derive/^([a-z]{8})/\\U$1/"
  - "derive/^([a-z]{9})/\\U$1/"
  - "derive/^([a-z]{10})/\\U$1/"
engine:
  filters:
    - uniquifier
  processors:
    - ascii_composer
    - key_binder
    - speller
    - recognizer
    - selector
    - navigator
    - express_editor
  segmentors:
    - matcher
    - ascii_segmentor
    - abc_segmentor
    - punct_segmentor
    - fallback_segmentor
  translators:
    - table_translator
    - punct_translator
key_binder:
  bindings:
    - {accept: KP_0, send: 0, when: composing}
    - {accept: KP_1, send: 1, when: composing}
    - {accept: KP_2, send: 2, when: composing}
    - {accept: KP_3, send: 3, when: composing}
    - {accept: KP_4, send: 4, when: composing}
    - {accept: KP_5, send: 5, when: composing}
    - {accept: KP_6, send: 6, when: composing}
    - {accept: KP_7, send: 7, when: composing}
    - {accept: KP_8, send: 8, when: composing}
    - {accept: KP_9, send: 9, when: composing}
    - {accept: KP_Decimal, send: period, when: composing}
    - {accept: KP_Multiply, send: asterisk, when: composing}
    - {accept: KP_Add, send: plus, when: composing}
    - {accept: KP_Subtract, send: minus, when: composing}
    - {accept: KP_Divide, send: slash, when: composing}
    - {accept: KP_Enter, send: Return, when: composing}
  import_preset: default
  select_first_character: bracketleft
  select_last_character: bracketright
menu:
  alternative_select_labels:
    - 1
    - 2
    - 3
    - 4
    - 5
    - 6
    - 7
    - 8
    - 9
    - 10
  page_size: 6
recognizer:
  import_preset: default
  patterns:
    email: "^[A-Za-z][-_.0-9A-Za-z]*@.*$"
    underscore: "^[A-Za-z]+_.*"
    url: "^(www[.]|https?:|ftp[.:]|mailto:|file:).*$|^[a-z]+[.].+$"
schema:
  description: "Easy English Nano，只包含少量常用词汇，方便中英文混合输入度方案调用。"
  name: "英文"
  schema_id: wanxiang_en
  version: "2023-10-17"
set_shuru_schema:
  - "derive/1([4-7|9])/$1teen/"
  - "derive/11/eleven/"
  - "derive/12/twelve/"
  - "derive/13/thirteen/"
  - "derive/15/fifteen/"
  - "derive/18/eighteen/"
  - "derive/0/o/"
  - "derive/0/O/"
  - "derive/0/zero/"
  - "derive/1/one/"
  - "derive/10/ten/"
  - "derive/2/to/"
  - "derive/2/two/"
  - "derive/3/three/"
  - "derive/4/for/"
  - "derive/4/four/"
  - "derive/5/five/"
  - "derive/6/six/"
  - "derive/7/seven/"
  - "derive/8/eight/"
  - "derive/9/nine/"
  - "derive/\\+/plus/"
  - "derive/\\./dot/"
  - "derive/@/at/"
  - "derive/-/hyphen/"
  - "derive/#/hash/"
  - "derive/#/number/"
  - "derive/#/sharp/"
  - "derive/♯/sharp/"
  - "derive / slash"
  - "derive/&/and/"
  - "derive/%/percent/"
  - "derive/[.]//"
  - "derive/[+]//"
  - "derive/[@]//"
  - "derive/[-]//"
  - "derive/[_]//"
  - "derive/[^a-zA-Z0-9]//"
  - "erase/^[^a-zA-Z0-9].+$/"
  - "derive/^.+$/\\L$0/"
  - "derive/^.+$/\\U$0/"
  - "derive/^./\\U$0/"
  - "derive/^([a-z]{2})/\\U$1/"
  - "derive/^([a-z]{3})/\\U$1/"
  - "derive/^([a-z]{4})/\\U$1/"
  - "derive/^([a-z]{5})/\\U$1/"
  - "derive/^([a-z]{6})/\\U$1/"
  - "derive/^([a-z]{7})/\\U$1/"
  - "derive/^([a-z]{8})/\\U$1/"
  - "derive/^([a-z]{9})/\\U$1/"
  - "derive/^([a-z]{10})/\\U$1/"
  - "derive/(?<!\\d)1([1-9])(?!\\d)/ui$1/"
  - "derive/([1-9])0000(?!0)/$1wj/"
  - "derive/([1-9])000(?!0)/$1qm/"
  - "derive/([1-9])00(?!0)/$1bd/"
  - "derive/([2-9])0(?!0)/$1ui/"
  - "derive/(?<!\\d)([2-9])([1-9])(?!\\d)/$1ui$2/"
  - "derive/\\./dm/"
  - "derive/10/ui/"
  - "derive/0/lk/"
  - "derive/1/yi/"
  - "derive/2/er/"
  - "derive/2/ll/"
  - "derive/3/sj/"
  - "derive/4/si/"
  - "derive/5/wu/"
  - "derive/6/lq/"
  - "derive/7/qi/"
  - "derive/8/ba/"
  - "derive/9/jq/"
  - "derive/\\+/jx/"
  - "derive/#/jk/"
speller:
  algebra:
    - "derive/1([4-7|9])/$1teen/"
    - "derive/11/eleven/"
    - "derive/12/twelve/"
    - "derive/13/thirteen/"
    - "derive/15/fifteen/"
    - "derive/18/eighteen/"
    - "derive/0/o/"
    - "derive/0/O/"
    - "derive/0/zero/"
    - "derive/1/one/"
    - "derive/10/ten/"
    - "derive/2/to/"
    - "derive/2/two/"
    - "derive/3/three/"
    - "derive/4/for/"
    - "derive/4/four/"
    - "derive/5/five/"
    - "derive/6/six/"
    - "derive/7/seven/"
    - "derive/8/eight/"
    - "derive/9/nine/"
    - "derive/\\+/plus/"
    - "derive/\\./dot/"
    - "derive/@/at/"
    - "derive/-/hyphen/"
    - "derive/#/hash/"
    - "derive/#/number/"
    - "derive/#/sharp/"
    - "derive/♯/sharp/"
    - "derive / slash"
    - "derive/&/and/"
    - "derive/%/percent/"
    - "derive/[.]//"
    - "derive/[+]//"
    - "derive/[@]//"
    - "derive/[-]//"
    - "derive/[_]//"
    - "derive/[^a-zA-Z0-9]//"
    - "erase/^[^a-zA-Z0-9].+$/"
    - "derive/^.+$/\\L$0/"
    - "derive/^.+$/\\U$0/"
    - "derive/^./\\U$0/"
    - "derive/^([a-z]{2})/\\U$1/"
    - "derive/^([a-z]{3})/\\U$1/"
    - "derive/^([a-z]{4})/\\U$1/"
    - "derive/^([a-z]{5})/\\U$1/"
    - "derive/^([a-z]{6})/\\U$1/"
    - "derive/^([a-z]{7})/\\U$1/"
    - "derive/^([a-z]{8})/\\U$1/"
    - "derive/^([a-z]{9})/\\U$1/"
    - "derive/^([a-z]{10})/\\U$1/"
    - "derive/(?<!\\d)1([1-9])(?!\\d)/ui$1/"
    - "derive/([1-9])0000(?!0)/$1wj/"
    - "derive/([1-9])000(?!0)/$1qm/"
    - "derive/([1-9])00(?!0)/$1bd/"
    - "derive/([2-9])0(?!0)/$1ui/"
    - "derive/(?<!\\d)([2-9])([1-9])(?!\\d)/$1ui$2/"
    - "derive/\\./dm/"
    - "derive/10/ui/"
    - "derive/0/lk/"
    - "derive/1/yi/"
    - "derive/2/er/"
    - "derive/2/ll/"
    - "derive/3/sj/"
    - "derive/4/si/"
    - "derive/5/wu/"
    - "derive/6/lq/"
    - "derive/7/qi/"
    - "derive/8/ba/"
    - "derive/9/jq/"
    - "derive/\\+/jx/"
    - "derive/#/jk/"
  alphabet: zyxwvutsrqponmlkjihgfedcbaZYXWVUTSRQPONMLKJIHGFEDCBA
  delimiter: " '"
switches:
  - name: ascii_mode
    reset: 0
    states: ["ASCII-OFF", "ASCII-ON"]
translator:
  dictionary: wanxiang_en
  spelling_hints: 9
"全拼":
  - "derive/1([4-7|9])/$1teen/"
  - "derive/11/eleven/"
  - "derive/12/twelve/"
  - "derive/13/thirteen/"
  - "derive/15/fifteen/"
  - "derive/18/eighteen/"
  - "derive/0/o/"
  - "derive/0/O/"
  - "derive/0/zero/"
  - "derive/1/one/"
  - "derive/10/ten/"
  - "derive/2/to/"
  - "derive/2/two/"
  - "derive/3/three/"
  - "derive/4/for/"
  - "derive/4/four/"
  - "derive/5/five/"
  - "derive/6/six/"
  - "derive/7/seven/"
  - "derive/8/eight/"
  - "derive/9/nine/"
  - "derive/\\+/plus/"
  - "derive/\\./dot/"
  - "derive/@/at/"
  - "derive/-/hyphen/"
  - "derive/#/hash/"
  - "derive/#/number/"
  - "derive/#/sharp/"
  - "derive/♯/sharp/"
  - "derive / slash"
  - "derive/&/and/"
  - "derive/%/percent/"
  - "derive/[.]//"
  - "derive/[+]//"
  - "derive/[@]//"
  - "derive/[-]//"
  - "derive/[_]//"
  - "derive/[^a-zA-Z0-9]//"
  - "erase/^[^a-zA-Z0-9].+$/"
  - "derive/^.+$/\\L$0/"
  - "derive/^.+$/\\U$0/"
  - "derive/^./\\U$0/"
  - "derive/^([a-z]{2})/\\U$1/"
  - "derive/^([a-z]{3})/\\U$1/"
  - "derive/^([a-z]{4})/\\U$1/"
  - "derive/^([a-z]{5})/\\U$1/"
  - "derive/^([a-z]{6})/\\U$1/"
  - "derive/^([a-z]{7})/\\U$1/"
  - "derive/^([a-z]{8})/\\U$1/"
  - "derive/^([a-z]{9})/\\U$1/"
  - "derive/^([a-z]{10})/\\U$1/"
  - "derive/(?<!\\d)1([1-9])(?!\\d)/shi$1/"
  - "derive/([1-9])0000(?!0)/$1wan/"
  - "derive/([1-9])000(?!0)/$1qian/"
  - "derive/([1-9])00(?!0)/$1bai/"
  - "derive/([2-9])0(?!0)/$1shi/"
  - "derive/(?<!\\d)([2-9])([1-9])(?!\\d)/$1shi$2/"
  - "derive/\\./dian/"
  - "derive/10/shi/"
  - "derive/0/ling/"
  - "derive/1/yi/"
  - "derive/2/er/"
  - "derive/2/liang/"
  - "derive/3/san/"
  - "derive/4/si/"
  - "derive/5/wu/"
  - "derive/6/liu/"
  - "derive/7/qi/"
  - "derive/8/ba/"
  - "derive/9/jiu/"
  - "derive/\\+/jia/"
  - "derive/#/jing/"
"小鹤双拼":
  - "derive/1([4-7|9])/$1teen/"
  - "derive/11/eleven/"
  - "derive/12/twelve/"
  - "derive/13/thirteen/"
  - "derive/15/fifteen/"
  - "derive/18/eighteen/"
  - "derive/0/o/"
  - "derive/0/O/"
  - "derive/0/zero/"
  - "derive/1/one/"
  - "derive/10/ten/"
  - "derive/2/to/"
  - "derive/2/two/"
  - "derive/3/three/"
  - "derive/4/for/"
  - "derive/4/four/"
  - "derive/5/five/"
  - "derive/6/six/"
  - "derive/7/seven/"
  - "derive/8/eight/"
  - "derive/9/nine/"
  - "derive/\\+/plus/"
  - "derive/\\./dot/"
  - "derive/@/at/"
  - "derive/-/hyphen/"
  - "derive/#/hash/"
  - "derive/#/number/"
  - "derive/#/sharp/"
  - "derive/♯/sharp/"
  - "derive / slash"
  - "derive/&/and/"
  - "derive/%/percent/"
  - "derive/[.]//"
  - "derive/[+]//"
  - "derive/[@]//"
  - "derive/[-]//"
  - "derive/[_]//"
  - "derive/[^a-zA-Z0-9]//"
  - "erase/^[^a-zA-Z0-9].+$/"
  - "derive/^.+$/\\L$0/"
  - "derive/^.+$/\\U$0/"
  - "derive/^./\\U$0/"
  - "derive/^([a-z]{2})/\\U$1/"
  - "derive/^([a-z]{3})/\\U$1/"
  - "derive/^([a-z]{4})/\\U$1/"
  - "derive/^([a-z]{5})/\\U$1/"
  - "derive/^([a-z]{6})/\\U$1/"
  - "derive/^([a-z]{7})/\\U$1/"
  - "derive/^([a-z]{8})/\\U$1/"
  - "derive/^([a-z]{9})/\\U$1/"
  - "derive/^([a-z]{10})/\\U$1/"
  - "derive/(?<!\\d)1([1-9])(?!\\d)/ui$1/"
  - "derive/([1-9])0000(?!0)/$1wj/"
  - "derive/([1-9])000(?!0)/$1qm/"
  - "derive/([1-9])00(?!0)/$1bd/"
  - "derive/([2-9])0(?!0)/$1ui/"
  - "derive/(?<!\\d)([2-9])([1-9])(?!\\d)/$1ui$2/"
  - "derive/\\./dm/"
  - "derive/10/ui/"
  - "derive/0/lk/"
  - "derive/1/yi/"
  - "derive/2/er/"
  - "derive/2/ll/"
  - "derive/3/sj/"
  - "derive/4/si/"
  - "derive/5/wu/"
  - "derive/6/lq/"
  - "derive/7/qi/"
  - "derive/8/ba/"
  - "derive/9/jq/"
  - "derive/\\+/jx/"
  - "derive/#/jk/"
"微软双拼":
  - "derive/1([4-7|9])/$1teen/"
  - "derive/11/eleven/"
  - "derive/12/twelve/"
  - "derive/13/thirteen/"
  - "derive/15/fifteen/"
  - "derive/18/eighteen/"
  - "derive/0/o/"
  - "derive/0/O/"
  - "derive/0/zero/"
  - "derive/1/one/"
  - "derive/10/ten/"
  - "derive/2/to/"
  - "derive/2/two/"
  - "derive/3/three/"
  - "derive/4/for/"
  - "derive/4/four/"
  - "derive/5/five/"
  - "derive/6/six/"
  - "derive/7/seven/"
  - "derive/8/eight/"
  - "derive/9/nine/"
  - "derive/\\+/plus/"
  - "derive/\\./dot/"
  - "derive/@/at/"
  - "derive/-/hyphen/"
  - "derive/#/hash/"
  - "derive/#/number/"
  - "derive/#/sharp/"
  - "derive/♯/sharp/"
  - "derive / slash"
  - "derive/&/and/"
  - "derive/%/percent/"
  - "derive/[.]//"
  - "derive/[+]//"
  - "derive/[@]//"
  - "derive/[-]//"
  - "derive/[_]//"
  - "derive/[^a-zA-Z0-9]//"
  - "erase/^[^a-zA-Z0-9].+$/"
  - "derive/^.+$/\\L$0/"
  - "derive/^.+$/\\U$0/"
  - "derive/^./\\U$0/"
  - "derive/^([a-z]{2})/\\U$1/"
  - "derive/^([a-z]{3})/\\U$1/"
  - "derive/^([a-z]{4})/\\U$1/"
  - "derive/^([a-z]{5})/\\U$1/"
  - "derive/^([a-z]{6})/\\U$1/"
  - "derive/^([a-z]{7})/\\U$1/"
  - "derive/^([a-z]{8})/\\U$1/"
  - "derive/^([a-z]{9})/\\U$1/"
  - "derive/^([a-z]{10})/\\U$1/"
  - "derive/(?<!\\d)1([1-9])(?!\\d)/ui$1/"
  - "derive/([1-9])0000(?!0)/$1wj/"
  - "derive/([1-9])000(?!0)/$1qm/"
  - "derive/([1-9])00(?!0)/$1bl/"
  - "derive/([2-9])0(?!0)/$1ui/"
  - "derive/(?<!\\d)([2-9])([1-9])(?!\\d)/$1ui$2/"
  - "derive/\\./dm/"
  - "derive/10/ui/"
  - "derive/0/l;/"
  - "derive/1/yi/"
  - "derive/2/er/"
  - "derive/2/or/"
  - "derive/2/ld/"
  - "derive/3/sj/"
  - "derive/4/si/"
  - "derive/5/wu/"
  - "derive/6/lq/"
  - "derive/7/qi/"
  - "derive/8/ba/"
  - "derive/9/jq/"
  - "derive/\\+/jw/"
  - "derive/#/j;/"
"拼音加加":
  - "derive/1([4-7|9])/$1teen/"
  - "derive/11/eleven/"
  - "derive/12/twelve/"
  - "derive/13/thirteen/"
  - "derive/15/fifteen/"
  - "derive/18/eighteen/"
  - "derive/0/o/"
  - "derive/0/O/"
  - "derive/0/zero/"
  - "derive/1/one/"
  - "derive/10/ten/"
  - "derive/2/to/"
  - "derive/2/two/"
  - "derive/3/three/"
  - "derive/4/for/"
  - "derive/4/four/"
  - "derive/5/five/"
  - "derive/6/six/"
  - "derive/7/seven/"
  - "derive/8/eight/"
  - "derive/9/nine/"
  - "derive/\\+/plus/"
  - "derive/\\./dot/"
  - "derive/@/at/"
  - "derive/-/hyphen/"
  - "derive/#/hash/"
  - "derive/#/number/"
  - "derive/#/sharp/"
  - "derive/♯/sharp/"
  - "derive / slash"
  - "derive/&/and/"
  - "derive/%/percent/"
  - "derive/[.]//"
  - "derive/[+]//"
  - "derive/[@]//"
  - "derive/[-]//"
  - "derive/[_]//"
  - "derive/[^a-zA-Z0-9]//"
  - "erase/^[^a-zA-Z0-9].+$/"
  - "derive/^.+$/\\L$0/"
  - "derive/^.+$/\\U$0/"
  - "derive/^./\\U$0/"
  - "derive/^([a-z]{2})/\\U$1/"
  - "derive/^([a-z]{3})/\\U$1/"
  - "derive/^([a-z]{4})/\\U$1/"
  - "derive/^([a-z]{5})/\\U$1/"
  - "derive/^([a-z]{6})/\\U$1/"
  - "derive/^([a-z]{7})/\\U$1/"
  - "derive/^([a-z]{8})/\\U$1/"
  - "derive/^([a-z]{9})/\\U$1/"
  - "derive/^([a-z]{10})/\\U$1/"
  - "derive/(?<!\\d)1([1-9])(?!\\d)/ii$1/"
  - "derive/([1-9])0000(?!0)/$1wf/"
  - "derive/([1-9])000(?!0)/$1qj/"
  - "derive/([1-9])00(?!0)/$1bs/"
  - "derive/([2-9])0(?!0)/$1ii/Add commentMore actions"
  - "derive/(?<!\\d)([2-9])([1-9])(?!\\d)/$1ii$2/"
  - "derive/\\./dj/"
  - "derive/10/ii/"
  - "derive/0/lq/"
  - "derive/1/yi/"
  - "derive/2/eq/"
  - "derive/2/lh/"
  - "derive/3/sf/"
  - "derive/4/si/"
  - "derive/5/wu/"
  - "derive/6/ln/"
  - "derive/7/qi/"
  - "derive/8/ba/"
  - "derive/9/jn/"
  - "derive/\\+/jb/"
  - "derive/#/jq/"
"搜狗双拼":
  - "derive/1([4-7|9])/$1teen/"
  - "derive/11/eleven/"
  - "derive/12/twelve/"
  - "derive/13/thirteen/"
  - "derive/15/fifteen/"
  - "derive/18/eighteen/"
  - "derive/0/o/"
  - "derive/0/O/"
  - "derive/0/zero/"
  - "derive/1/one/"
  - "derive/10/ten/"
  - "derive/2/to/"
  - "derive/2/two/"
  - "derive/3/three/"
  - "derive/4/for/"
  - "derive/4/four/"
  - "derive/5/five/"
  - "derive/6/six/"
  - "derive/7/seven/"
  - "derive/8/eight/"
  - "derive/9/nine/"
  - "derive/\\+/plus/"
  - "derive/\\./dot/"
  - "derive/@/at/"
  - "derive/-/hyphen/"
  - "derive/#/hash/"
  - "derive/#/number/"
  - "derive/#/sharp/"
  - "derive/♯/sharp/"
  - "derive / slash"
  - "derive/&/and/"
  - "derive/%/percent/"
  - "derive/[.]//"
  - "derive/[+]//"
  - "derive/[@]//"
  - "derive/[-]//"
  - "derive/[_]//"
  - "derive/[^a-zA-Z0-9]//"
  - "erase/^[^a-zA-Z0-9].+$/"
  - "derive/^.+$/\\L$0/"
  - "derive/^.+$/\\U$0/"
  - "derive/^./\\U$0/"
  - "derive/^([a-z]{2})/\\U$1/"
  - "derive/^([a-z]{3})/\\U$1/"
  - "derive/^([a-z]{4})/\\U$1/"
  - "derive/^([a-z]{5})/\\U$1/"
  - "derive/^([a-z]{6})/\\U$1/"
  - "derive/^([a-z]{7})/\\U$1/"
  - "derive/^([a-z]{8})/\\U$1/"
  - "derive/^([a-z]{9})/\\U$1/"
  - "derive/^([a-z]{10})/\\U$1/"
  - "derive/(?<!\\d)1([1-9])(?!\\d)/ui$1/"
  - "derive/([1-9])0000(?!0)/$1wj/"
  - "derive/([1-9])000(?!0)/$1qm/"
  - "derive/([1-9])00(?!0)/$1bl/"
  - "derive/([2-9])0(?!0)/$1ui/"
  - "derive/(?<!\\d)([2-9])([1-9])(?!\\d)/$1ui$2/"
  - "derive/\\./dm/"
  - "derive/10/ui/"
  - "derive/0/l;/"
  - "derive/1/yi/"
  - "derive/2/er/"
  - "derive/2/or/"
  - "derive/2/ld/"
  - "derive/3/sj/"
  - "derive/4/si/"
  - "derive/5/wu/"
  - "derive/6/lq/"
  - "derive/7/qi/"
  - "derive/8/ba/"
  - "derive/9/jq/"
  - "derive/\\+/jw/"
  - "derive/#/jy/"
"智能ABC":
  - "derive/1([4-7|9])/$1teen/"
  - "derive/11/eleven/"
  - "derive/12/twelve/"
  - "derive/13/thirteen/"
  - "derive/15/fifteen/"
  - "derive/18/eighteen/"
  - "derive/0/o/"
  - "derive/0/O/"
  - "derive/0/zero/"
  - "derive/1/one/"
  - "derive/10/ten/"
  - "derive/2/to/"
  - "derive/2/two/"
  - "derive/3/three/"
  - "derive/4/for/"
  - "derive/4/four/"
  - "derive/5/five/"
  - "derive/6/six/"
  - "derive/7/seven/"
  - "derive/8/eight/"
  - "derive/9/nine/"
  - "derive/\\+/plus/"
  - "derive/\\./dot/"
  - "derive/@/at/"
  - "derive/-/hyphen/"
  - "derive/#/hash/"
  - "derive/#/number/"
  - "derive/#/sharp/"
  - "derive/♯/sharp/"
  - "derive / slash"
  - "derive/&/and/"
  - "derive/%/percent/"
  - "derive/[.]//"
  - "derive/[+]//"
  - "derive/[@]//"
  - "derive/[-]//"
  - "derive/[_]//"
  - "derive/[^a-zA-Z0-9]//"
  - "erase/^[^a-zA-Z0-9].+$/"
  - "derive/^.+$/\\L$0/"
  - "derive/^.+$/\\U$0/"
  - "derive/^./\\U$0/"
  - "derive/^([a-z]{2})/\\U$1/"
  - "derive/^([a-z]{3})/\\U$1/"
  - "derive/^([a-z]{4})/\\U$1/"
  - "derive/^([a-z]{5})/\\U$1/"
  - "derive/^([a-z]{6})/\\U$1/"
  - "derive/^([a-z]{7})/\\U$1/"
  - "derive/^([a-z]{8})/\\U$1/"
  - "derive/^([a-z]{9})/\\U$1/"
  - "derive/^([a-z]{10})/\\U$1/"
  - "derive/(?<!\\d)1([1-9])(?!\\d)/vi$1/"
  - "derive/([1-9])0000(?!0)/$1wj/"
  - "derive/([1-9])000(?!0)/$1qw/"
  - "derive/([1-9])00(?!0)/$1bl/"
  - "derive/([2-9])0(?!0)/$1vi/"
  - "derive/(?<!\\d)([2-9])([1-9])(?!\\d)/$1vi$2/"
  - "derive/\\./dw/"
  - "derive/10/vi/"
  - "derive/0/ly/"
  - "derive/1/yi/"
  - "derive/2/er/"
  - "derive/2/or/"
  - "derive/2/lt/"
  - "derive/3/sj/"
  - "derive/4/si/"
  - "derive/5/wu/"
  - "derive/6/lr/"
  - "derive/7/qi/"
  - "derive/8/ba/"
  - "derive/9/jr/"
  - "derive/\\+/jd/"
  - "derive/#/jy/"
"紫光双拼":
  - "derive/1([4-7|9])/$1teen/"
  - "derive/11/eleven/"
  - "derive/12/twelve/"
  - "derive/13/thirteen/"
  - "derive/15/fifteen/"
  - "derive/18/eighteen/"
  - "derive/0/o/"
  - "derive/0/O/"
  - "derive/0/zero/"
  - "derive/1/one/"
  - "derive/10/ten/"
  - "derive/2/to/"
  - "derive/2/two/"
  - "derive/3/three/"
  - "derive/4/for/"
  - "derive/4/four/"
  - "derive/5/five/"
  - "derive/6/six/"
  - "derive/7/seven/"
  - "derive/8/eight/"
  - "derive/9/nine/"
  - "derive/\\+/plus/"
  - "derive/\\./dot/"
  - "derive/@/at/"
  - "derive/-/hyphen/"
  - "derive/#/hash/"
  - "derive/#/number/"
  - "derive/#/sharp/"
  - "derive/♯/sharp/"
  - "derive / slash"
  - "derive/&/and/"
  - "derive/%/percent/"
  - "derive/[.]//"
  - "derive/[+]//"
  - "derive/[@]//"
  - "derive/[-]//"
  - "derive/[_]//"
  - "derive/[^a-zA-Z0-9]//"
  - "erase/^[^a-zA-Z0-9].+$/"
  - "derive/^.+$/\\L$0/"
  - "derive/^.+$/\\U$0/"
  - "derive/^./\\U$0/"
  - "derive/^([a-z]{2})/\\U$1/"
  - "derive/^([a-z]{3})/\\U$1/"
  - "derive/^([a-z]{4})/\\U$1/"
  - "derive/^([a-z]{5})/\\U$1/"
  - "derive/^([a-z]{6})/\\U$1/"
  - "derive/^([a-z]{7})/\\U$1/"
  - "derive/^([a-z]{8})/\\U$1/"
  - "derive/^([a-z]{9})/\\U$1/"
  - "derive/^([a-z]{10})/\\U$1/"
  - "derive/(?<!\\d)1([1-9])(?!\\d)/ii$1/"
  - "derive/([1-9])0000(?!0)/$1wr/"
  - "derive/([1-9])000(?!0)/$1qf/"
  - "derive/([1-9])00(?!0)/$1bp/"
  - "derive/([2-9])0(?!0)/$1ii/"
  - "derive/(?<!\\d)([2-9])([1-9])(?!\\d)/$1ii$2/"
  - "derive/\\./df/"
  - "derive/10/ii/"
  - "derive/0/l;/"
  - "derive/1/yi/"
  - "derive/2/er/"
  - "derive/2/oj/"
  - "derive/2/lg/"
  - "derive/3/sr/"
  - "derive/4/si/"
  - "derive/5/wu/"
  - "derive/6/lj/"
  - "derive/7/qi/"
  - "derive/8/ba/"
  - "derive/9/jj/"
  - "derive/\\+/jx/"
  - "derive/#/j;/"
"自然码":
  - "derive/1([4-7|9])/$1teen/"
  - "derive/11/eleven/"
  - "derive/12/twelve/"
  - "derive/13/thirteen/"
  - "derive/15/fifteen/"
  - "derive/18/eighteen/"
  - "derive/0/o/"
  - "derive/0/O/"
  - "derive/0/zero/"
  - "derive/1/one/"
  - "derive/10/ten/"
  - "derive/2/to/"
  - "derive/2/two/"
  - "derive/3/three/"
  - "derive/4/for/"
  - "derive/4/four/"
  - "derive/5/five/"
  - "derive/6/six/"
  - "derive/7/seven/"
  - "derive/8/eight/"
  - "derive/9/nine/"
  - "derive/\\+/plus/"
  - "derive/\\./dot/"
  - "derive/@/at/"
  - "derive/-/hyphen/"
  - "derive/#/hash/"
  - "derive/#/number/"
  - "derive/#/sharp/"
  - "derive/♯/sharp/"
  - "derive / slash"
  - "derive/&/and/"
  - "derive/%/percent/"
  - "derive/[.]//"
  - "derive/[+]//"
  - "derive/[@]//"
  - "derive/[-]//"
  - "derive/[_]//"
  - "derive/[^a-zA-Z0-9]//"
  - "erase/^[^a-zA-Z0-9].+$/"
  - "derive/^.+$/\\L$0/"
  - "derive/^.+$/\\U$0/"
  - "derive/^./\\U$0/"
  - "derive/^([a-z]{2})/\\U$1/"
  - "derive/^([a-z]{3})/\\U$1/"
  - "derive/^([a-z]{4})/\\U$1/"
  - "derive/^([a-z]{5})/\\U$1/"
  - "derive/^([a-z]{6})/\\U$1/"
  - "derive/^([a-z]{7})/\\U$1/"
  - "derive/^([a-z]{8})/\\U$1/"
  - "derive/^([a-z]{9})/\\U$1/"
  - "derive/^([a-z]{10})/\\U$1/"
  - "derive/(?<!\\d)1([1-9])(?!\\d)/ui$1/"
  - "derive/([1-9])0000(?!0)/$1wj/"
  - "derive/([1-9])000(?!0)/$1qm/"
  - "derive/([1-9])00(?!0)/$1bl/"
  - "derive/([2-9])0(?!0)/$1ui/"
  - "derive/(?<!\\d)([2-9])([1-9])(?!\\d)/$1ui$2/"
  - "derive/\\./dm/"
  - "derive/10/ui/"
  - "derive/0/ly/"
  - "derive/1/yi/"
  - "derive/2/er/"
  - "derive/2/ld/"
  - "derive/3/sj/"
  - "derive/4/si/"
  - "derive/5/wu/"
  - "derive/6/lq/"
  - "derive/7/qi/"
  - "derive/8/ba/"
  - "derive/9/jq/"
  - "derive/\\+/jw/"
  - "derive/#/jy/"