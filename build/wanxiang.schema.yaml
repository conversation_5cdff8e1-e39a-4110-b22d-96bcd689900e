"14键":
  __append:
    - "xform/w/q/"
    - "xform/r/e/"
    - "xform/y/t/"
    - "xform/i/u/"
    - "xform/p/o/"
    - "xform/s/a/"
    - "xform/f/d/"
    - "xform/h/g/"
    - "xform/k/j/"
    - "xform/x/z/"
    - "xform/v/c/"
    - "xform/n/b/"
"18键":
  __append:
    - "xform/e/w/"
    - "xform/t/r/"
    - "xform/o/i/"
    - "xform/d/s/"
    - "xform/g/f/"
    - "xform/k/j/"
    - "xform/c/x/"
    - "xform/n/b/"
"26键":
  __append:
    []
"9键":
  __append:
    - "xform/b/a/"
    - "xform/c/a/"
    - "xform/e/d/"
    - "xform/f/d/"
    - "xform/h/g/"
    - "xform/i/g/"
    - "xform/k/j/"
    - "xform/l/j/"
    - "xform/n/m/"
    - "xform/o/m/"
    - "xform/q/p/"
    - "xform/r/p/"
    - "xform/s/p/"
    - "xform/u/t/"
    - "xform/v/t/"
    - "xform/x/w/"
    - "xform/y/w/"
    - "xform/z/w/"
__build_info:
  rime_version: 1.13.0
  timestamps:
    default: 1751383420
    default.custom: 1751383723
    wanxiang.custom: 1751383209
    wanxiang.schema: 1751382402
    wanxiang_symbols: 1751372775
    wanxiang_symbols.custom: 0
add_user_dict:
  dictionary: wanxiang
  enable_charset_filter: false
  enable_completion: true
  enable_encoder: true
  enable_sentence: true
  enable_user_dict: false
  encode_commit_history: false
  initial_quality: "-1"
  prefix: "``"
  tag: add_user_dict
  tips: "〔开始造词〕"
  user_dict: zc
birthday_reminder:
  lunar_birthdays:
    "小明": 0114
    "小红": "0815,农历中秋"
  solar_birthdays:
    "大明": 0405
    "小明": "0501,准备礼物"
calculator:
  trigger: V
chengyu:
  db_class: tabledb
  dictionary: ""
  enable_completion: false
  enable_sentence: false
  initial_quality: 1.2
  user_dict: "jm_dicts/chengyu"
chinese_english:
  comment_format:
    - "xform/-/ /"
  opencc_config: chinese_english.json
  option_name: chinese_english
  tips: char
cn_en:
  comment_format:
    - "xform/^.+$//"
  db_class: tabledb
  dictionary: ""
  enable_completion: true
  enable_sentence: false
  initial_quality: 0.5
  user_dict: "en_dicts/flypy"
custom_phrase:
  db_class: tabledb
  dictionary: ""
  enable_completion: false
  enable_sentence: false
  initial_quality: 99
  user_dict: my_custom_phrase
editor:
  bindings:
    BackSpace: revert
    "Control+BackSpace": back_syllable
    "Control+Delete": delete_candidate
    "Control+Return": commit_script_text
    "Control+Shift+Return": commit_comment
    Delete: delete
    Escape: cancel
    Return: commit_raw_input
    space: confirm
emoji:
  inherit_comment: false
  opencc_config: emoji.json
  option_name: emoji
engine:
  filters:
    - "lua_filter@*super_sequence*F"
    - "lua_filter@*autocap_filter"
    - "reverse_lookup_filter@radical_reverse_lookup"
    - "lua_filter@*super_preedit"
    - "simplifier@emoji"
    - "simplifier@s2t"
    - "simplifier@s2tw"
    - "simplifier@s2hk"
    - "simplifier@chinese_english"
    - "lua_filter@*search@wanxiang_radical"
    - "lua_filter@*super_tips*M"
    - "lua_filter@*super_comment"
    - "lua_filter@*text_formatting"
    - uniquifier
  processors:
    - predictor
    - chord_composer
    - "lua_processor@*select_character"
    - "lua_processor@*quick_symbol_text"
    - "lua_processor@*super_tips*S"
    - "lua_processor@*tone_fallback"
    - "lua_processor@*super_sequence*P"
    - "lua_processor@*limit_repeated"
    - "lua_processor@*backspace_limit"
    - "lua_processor@*userdb_sync_delete"
    - ascii_composer
    - recognizer
    - key_binder
    - "lua_processor@*key_binder"
    - speller
    - punctuator
    - selector
    - navigator
    - express_editor
  segmentors:
    - ascii_segmentor
    - matcher
    - abc_segmentor
    - "affix_segmentor@radical_lookup"
    - "affix_segmentor@reverse_stroke"
    - "affix_segmentor@add_user_dict"
    - punct_segmentor
    - fallback_segmentor
  translators:
    - predict_translator
    - punct_translator
    - script_translator
    - "lua_translator@*shijian"
    - "lua_translator@*unicode"
    - "lua_translator@*number_translator"
    - "lua_translator@*super_calculator"
    - "lua_translator@*input_statistics"
    - "table_translator@custom_phrase"
    - "table_translator@wanxiang_en"
    - "table_translator@cn_en"
    - "table_translator@chengyu"
    - "table_translator@radical_lookup"
    - "table_translator@reverse_stroke"
    - "script_translator@add_user_dict"
    - "script_translator@user_dict_set"
    - "lua_translator@*force_gc"
grammar:
  collocation_max_length: 8
  collocation_min_length: 2
  collocation_penalty: "-12"
  language: "wanxiang-lts-zh-hans"
  non_collocation_penalty: "-12"
  rear_penalty: "-18"
  weak_collocation_penalty: "-24"
key_binder:
  bindings:
    - {accept: KP_0, send: 0, when: composing}
    - {accept: KP_1, send: 1, when: composing}
    - {accept: KP_2, send: 2, when: composing}
    - {accept: KP_3, send: 3, when: composing}
    - {accept: KP_4, send: 4, when: composing}
    - {accept: KP_5, send: 5, when: composing}
    - {accept: KP_6, send: 6, when: composing}
    - {accept: KP_7, send: 7, when: composing}
    - {accept: KP_8, send: 8, when: composing}
    - {accept: KP_9, send: 9, when: composing}
    - {accept: KP_Decimal, send: period, when: composing}
    - {accept: KP_Multiply, send: asterisk, when: composing}
    - {accept: KP_Add, send: plus, when: composing}
    - {accept: KP_Subtract, send: minus, when: composing}
    - {accept: KP_Divide, send: slash, when: composing}
    - {accept: KP_Enter, send: Return, when: composing}
    - {accept: "Control+w", send: "Control+BackSpace", when: composing}
    - {accept: "Control+e", toggle: chinese_english, when: has_menu}
    - {accept: "Control+a", toggle: tone_hint, when: has_menu}
    - {accept: "Control+s", toggle: tone_display, when: has_menu}
    - {accept: "Control+t", toggle: super_tips, when: has_menu}
    - {accept: Tab, send: "Control+Right", when: has_menu}
    - {accept: Tab, send: "Control+Right", when: composing}
    - {accept: "Control+Tab", send_sequence: "{Home}{Shift+Right}{1}{Shift+Right}", when: composing}
    - {accept: "Control+1", send_sequence: "{Home}{Shift+Right}", when: composing}
    - {accept: "Control+2", send_sequence: "{Home}{Shift+Right}{Shift+Right}", when: composing}
    - {accept: "Control+3", send_sequence: "{Home}{Shift+Right}{Shift+Right}{Shift+Right}", when: composing}
    - {accept: "Control+4", send_sequence: "{Home}{Shift+Right}{Shift+Right}{Shift+Right}{Shift+Right}", when: composing}
    - {accept: "Control+5", send_sequence: "{Home}{Shift+Right}{Shift+Right}{Shift+Right}{Shift+Right}{Shift+Right}", when: composing}
    - {accept: "Control+6", send_sequence: "{Home}{Shift+Right}{Shift+Right}{Shift+Right}{Shift+Right}{Shift+Right}{Shift+Right}", when: composing}
    - {accept: "`", match: "^.*`$", send_sequence: "{BackSpace}{Home}{`}{`}{End}"}
    - {accept: minus, send: Page_Up, when: has_menu}
    - {accept: equal, send: Page_Down, when: has_menu}
    - {accept: "Alt+Left", send: "Shift+Left", when: composing}
    - {accept: "Alt+Right", send: "Shift+Right", when: composing}
    - {accept: "Control+Shift+3", toggle: ascii_punct, when: always}
    - {accept: "Control+Shift+numbersign", toggle: ascii_punct, when: always}
    - {accept: "Control+Shift+4", toggle: s2t, when: always}
    - {accept: "Control+Shift+dollar", toggle: s2t, when: always}
  import_preset: default
  search: "`"
  select_first_character: bracketleft
  select_last_character: bracketright
  shijian_keys:
    - "/"
    - o
  tips_key: period
menu:
  alternative_select_labels:
    - 1
    - 2
    - 3
    - 4
    - 5
    - 6
    - 7
    - 8
    - 9
    - 10
  page_size: 6
octagram:
  grammar:
    collocation_max_length: 8
    collocation_min_length: 2
    collocation_penalty: "-12"
    language: "wanxiang-lts-zh-hans"
    non_collocation_penalty: "-12"
    rear_penalty: "-18"
    weak_collocation_penalty: "-24"
  translator:
    contextual_suggestions: false
    max_homographs: 5
    max_homophones: 5
predictor:
  db: "wanxiang-lts-zh-predict.db"
  max_candidates: 5
  max_iterations: 1
punctuator:
  digit_separators: ":,."
  full_shape:
    " ": {commit: "　"}
    "!": {commit: "！"}
    "\"": {pair: ["“", "”"]}
    "#": ["＃", "⌘"]
    "$": ["￥", "$", "€", "£", "¥", "¢", "¤"]
    "%": ["％", "°", "℃"]
    "&": "＆"
    "'": {pair: ["‘", "’"]}
    "(": "（"
    ")": "）"
    "*": ["＊", "·", "・", "×", "※", "❂"]
    "+": "＋"
    ",": {commit: "，"}
    "-": "－"
    .: {commit: "。"}
    "/": ["／", "÷"]
    ":": {commit: "："}
    ";": {commit: "；"}
    "<": ["《", "〈", "«", "‹"]
    "=": "＝"
    ">": ["》", "〉", "»", "›"]
    "?": {commit: "？"}
    "@": ["＠", "☯"]
    "[": ["「", "【", "〔", "［"]
    "\\": ["、", "＼"]
    "]": ["」", "】", "〕", "］"]
    "^": {commit: "……"}
    _: "——"
    "`": "｀"
    "{": ["『", "〖", "｛"]
    "|": ["·", "｜", "§", "¦"]
    "}": ["』", "〗", "｝"]
    "~": "～"
  half_shape:
    "!": "！"
    "\"": {pair: ["“", "”"]}
    "#": "#"
    "$": "¥"
    "%": "%"
    "&": "&"
    "'": {pair: ["‘", "’"]}
    "(": "（"
    ")": "）"
    "*": "*"
    "+": "+"
    ",": "，"
    "-": "-"
    .: "。"
    "/": ["/", "÷"]
    ":": "："
    ";": "；"
    "<": "《"
    "=": "="
    ">": "》"
    "?": "？"
    "@": "@"
    "[": "【"
    "\\": "、"
    "]": "】"
    "^": "……"
    _: "——"
    "`": ["`", "```"]
    "{": "「"
    "|": "|"
    "}": "」"
    "~": "~"
  symbols:
    "/0": ["零", "〇", "⁰", "₀", "⓪", "⓿", "０"]
    "/1": ["一", "壹", "¹", "₁", "Ⅰ", "ⅰ", "①", "➀", "❶", "➊", "⓵", "⑴", "⒈", "１", "㊀", "㈠", "弌", "壱", "幺", "㆒"]
    "/10": ["十", "拾", "¹⁰", "₁₀", "Ⅹ", "ⅹ", "⑩", "➉", "❿", "➓", "⓾", "⑽", "⒑", "１０", "㊉", "㈩", "什"]
    "/2": ["二", "贰", "²", "₂", "Ⅱ", "ⅱ", "②", "➁", "❷", "➋", "⓶", "⑵", "⒉", "２", "㊁", "㈡", "弍", "弐", "貮", "㒃", "㒳", "两", "俩", "㆓"]
    "/3": ["三", "叁", "³", "₃", "Ⅲ", "ⅲ", "③", "➂", "❸", "➌", "⓷", "⑶", "⒊", "３", "㊂", "㈢", "参", "参", "叁", "弎", "仨", "㆔"]
    "/4": ["四", "肆", "⁴", "₄", "Ⅳ", "ⅳ", "④", "➃", "❹", "➍", "⓸", "⑷", "⒋", "４", "㊃", "㈣", "亖"]
    "/5": ["五", "伍", "⁵", "₅", "Ⅴ", "ⅴ", "⑤", "➄", "❺", "➎", "⓹", "⑸", "⒌", "５", "㊄", "㈤", "㐅", "㠪", "𠄡"]
    "/6": ["六", "陆", "⁶", "₆", "Ⅵ", "ⅵ", "⑥", "➅", "❻", "➏", "⓺", "⑹", "⒍", "６", "㊅", "㈥", "ↅ"]
    "/7": ["七", "柒", "⁷", "₇", "Ⅶ", "ⅶ", "⑦", "➆", "❼", "➐", "⓻", "⑺", "⒎", "７", "㊆", "㈦", "漆"]
    "/8": ["八", "捌", "⁸", "₈", "Ⅷ", "ⅷ", "⑧", "➇", "❽", "➑", "⓼", "⑻", "⒏", "８", "㊇", "㈧"]
    "/9": ["九", "玖", "⁹", "₉", "Ⅸ", "ⅸ", "⑨", "➈", "❾", "➒", "⓽", "⑼", "⒐", "９", "㊈", "㈨"]
    "/A": ["Ā", "Á", "Ǎ", "À", "Ȁ", "Â", "Ă", "Ȃ", "Ȧ", "Ä", "Å", "Ã", "ᴀ", "ᴬ", "Ⱥ", "Ả", "Ą", "Ạ", "Ḁ", "Ấ", "Ầ", "Ẫ", "Ẩ", "Ắ", "Ằ", "Ẵ", "Ẳ", "Ǡ", "Ǟ", "Ǻ", "Ậ", "Ặ", "Ɐ", "Ɑ", "Ɒ"]
    "/AA": ["Ꜳ"]
    "/AE": ["Æ", "Ǣ", "Ǽ", "ᴭ", "ᴁ"]
    "/AO": ["Ꜵ"]
    "/AU": ["Ꜷ"]
    "/AV": ["Ꜹ", "Ꜻ"]
    "/AY": ["Ꜽ"]
    "/B": ["Ḃ", "Ḅ", "ʙ", "ᴃ", "ᴮ", "ᴯ", "Ƀ", "Ƃ", "Ḇ", "Ɓ", "Ꞗ"]
    "/C": ["Ç", "Ć", "Č", "Ĉ", "Ċ", "ᴄ", "Ȼ", "Ꞓ", "Ƈ", "Ḉ", "Ꜿ"]
    "/D": ["Ď", "Ḋ", "ᴅ", "ᴆ", "ᴰ", "Đ", "Ƌ", "Ḑ", "Ḓ", "Ḏ", "Ḍ", "Ɖ", "Ɗ", "Ð", "Ǳ", "ǲ", "Ǆ", "ǅ"]
    "/E": ["Ē", "É", "Ě", "È", "Ȅ", "Ê", "Ĕ", "Ȇ", "Ė", "Ë", "Ẽ", "ᴇ", "ᴱ", "Ɇ", "Ẻ", "Ȩ", "Ę", "Ḙ", "Ẹ", "Ḛ", "Ḗ", "Ḕ", "Ế", "Ề", "Ễ", "Ể", "Ḝ", "Ệ", "Ə", "Ɛ", "Ɜ", "Ǝ", "ⱻ", "ᴲ", "Ȝ"]
    "/F": ["Ḟ", "ꜰ", "Ƒ", "Ꞙ", "ꟻ"]
    "/G": ["Ḡ", "Ǵ", "Ǧ", "Ĝ", "Ğ", "Ġ", "ʛ", "ᴳ", "Ǥ", "Ꞡ", "Ģ", "Ɠ", "Ɡ", "Ɣ"]
    "/H": ["Ĥ", "Ȟ", "Ḣ", "Ḧ", "ʜ", "ᴴ", "Ħ", "Ɦ", "Ꜧ", "Ḩ", "Ḫ", "Ḥ", "Ⱨ", "Ɥ", "Ⱶ"]
    "/HV": ["Ƕ"]
    "/I": ["Ī", "Í", "Ǐ", "Ì", "Ȉ", "Î", "Ĭ", "Ȋ", "Ï", "Ĩ", "ɪ", "ᴵ", "ᶦ", "Ɨ", "ᵻ", "ᶧ", "Ỉ", "Į", "Ị", "Ḭ", "Ḯ", "ꟾ", "Ɩ"]
    "/IJ": ["Ĳ"]
    "/J": ["Ĵ", "ᴊ", "ᴶ", "Ɉ", "Ʝ"]
    "/K": ["Ḱ", "Ǩ", "ᴋ", "ᴷ", "Ꝁ", "Ꝃ", "Ꞣ", "Ꝅ", "Ķ", "Ḵ", "Ḳ", "Ƙ", "Ⱪ", "Ʞ"]
    "/L": ["Ĺ", "ʟ", "ᶫ", "Ƚ", "Ꝉ", "Ł", "ᴌ", "Ⱡ", "Ɫ", "Ɬ", "Ľ", "Ļ", "Ḻ", "Ḽ", "Ḷ", "Ŀ", "Ꝇ"]
    "/LL": ["Ỻ"]
    "/M": ["Ḿ", "Ṁ", "ᴍ", "ᴹ", "Ṃ", "Ɱ", "Ɯ", "ꟽ", "ꟿ"]
    "/N": ["Ń", "Ň", "Ǹ", "Ṅ", "Ñ", "ɴ", "ᴺ", "ᴻ", "ᶰ", "Ɲ", "Ƞ", "Ŋ", "Ņ", "Ṉ", "Ṋ", "Ṇ", "Ꞑ"]
    "/NJ": ["Ǌ"]
    "/Nj": ["ǋ"]
    "/O": ["Ō", "Ó", "Ő", "Ǒ", "Ò", "Ô", "Ŏ", "Ȯ", "Ö", "Õ", "ᴏ", "ᴼ", "Ɔ", "ᴐ", "Ø", "Ǫ", "Ọ", "Ơ", "Ɵ", "Ꝋ", "Ꝍ", "Ṓ", "Ṑ", "Ố", "Ồ", "Ỗ", "Ổ", "Ȱ", "Ȫ", "Ȭ", "Ṍ", "Ṏ", "Ộ", "Ǭ", "Ǿ", "Ớ", "Ờ", "Ỡ", "Ở", "Ợ"]
    "/OE": ["Œ", "ɶ"]
    "/OI": ["Ƣ"]
    "/OO": ["Ꝏ"]
    "/OU": ["Ȣ", "ᴽ"]
    "/P": ["Ṕ", "Ṗ", "ᴘ", "ᴾ", "Ᵽ", "Ꝑ", "Ƥ", "Ꝓ", "Ꝕ", "ꟼ"]
    "/Q": ["Ɋ", "Ꝗ", "Ꝙ"]
    "/R": ["Ŕ", "Ř", "Ȑ", "Ȓ", "Ṙ", "ʀ", "ᴙ", "ᴿ", "Ʀ", "ꭆ", "Ɍ", "Ꞧ", "Ŗ", "Ṟ", "Ṛ", "Ṝ", "Ɽ", "ꝶ", "ʶ", "ʁ", "Ꝛ", "Ꝝ"]
    "/Rx": ["℞"]
    "/S": ["Ś", "Ŝ", "Š", "Ṡ", "ꜱ", "Ꞩ", "Ş", "Ṣ", "Ș", "Ṥ", "Ṧ", "Ṩ", "Ʃ", "ẞ"]
    "/T": ["Ť", "Ṫ", "ᴛ", "ᵀ", "Ʈ", "Þ", "Ꝥ", "Ꝧ", "Ŧ", "Ⱦ", "Ţ", "Ṯ", "Ṱ", "Ṭ", "Ț", "Ƭ", "Ʇ"]
    "/TZ": ["Ꜩ"]
    "/U": ["Ū", "Ú", "Ű", "Ǔ", "Ù", "Ȕ", "Û", "Ŭ", "Ȗ", "Ü", "Ǖ", "Ǘ", "Ǚ", "Ǜ", "Ů", "Ũ", "ᴜ", "ᵁ", "ᶸ", "Ʉ", "Ủ", "Ų", "Ṷ", "Ụ", "Ṳ", "Ṵ", "Ư", "Ʊ", "Ṻ", "Ṹ", "Ứ", "Ừ", "Ữ", "Ử", "Ự"]
    "/V": ["Ü", "Ǖ", "Ǘ", "Ǚ", "Ǜ", "Ṽ", "ᴠ", "ⱽ", "Ṿ", "Ꝟ", "Ʋ", "Ỽ", "Ʌ"]
    "/VY": ["Ꝡ"]
    "/W": ["Ẃ", "Ẁ", "Ŵ", "Ẇ", "Ẅ", "W̊", "ᴡ", "ᵂ", "Ẉ", "Ƿ", "Ⱳ"]
    "/X": ["Ẋ", "Ẍ"]
    "/Y": ["Ȳ", "Ý", "Ỳ", "Ŷ", "Ẏ", "Ÿ", "Ỹ", "ʏ", "Ɏ", "Ỷ", "Ỵ", "Ƴ", "Ỿ"]
    "/Z": ["Ź", "Ž", "Ẑ", "Ż", "ᴢ", "Ƶ", "Ẕ", "Ẓ", "Ȥ", "Ⱬ", "Ʒ", "ᴣ", "Ǯ", "Ƹ", "Ɀ", "Ꝣ"]
    "/a": ["ā", "á", "ǎ", "à", "ȁ", "â", "ă", "ȃ", "ȧ", "ä", "å", "ã", "ₐ", "ᵃ", "ª", "ⱥ", "ꬰ", "ả", "ą", "ạ", "ḁ", "ẚ", "ấ", "ầ", "ẫ", "ẩ", "ắ", "ằ", "ẵ", "ẳ", "ǡ", "ǟ", "ǻ", "ậ", "ặ", "ᶏ", "ɐ", "ᵄ", "ɑ", "ᵅ", "ᶐ", "ɒ", "ᶛ"]
    "/aa": ["ꜳ"]
    "/ae": ["æ", "ǣ", "ǽ", "ᵆ", "ᴂ"]
    "/ao": ["ꜵ"]
    "/au": ["ꜷ"]
    "/av": ["ꜹ", "ꜻ"]
    "/ay": ["ꜽ"]
    "/b": ["ḃ", "ḅ", "ᵇ", "ƀ", "ƃ", "ḇ", "ɓ", "ᵬ", "ᶀ", "ꞗ"]
    "/bd": ["、", "。", "「", "」", "『", "』", "【", "】", "〈", "〉", "《", "》", "₋", "⁻", "―", "˗", "ˉ", "＿", "﹍", "﹎", "．", "¡", "‼", "⁉", "¿", "؟", "⁈", "⁇", "､", "｡", "〃", "〄", "々", "〆", "〇", "〒", "〓", "〔", "〕", "〖", "〗", "〘", "〙", "〚", "〛", "〜", "〝", "〞", "〟", "〠", "〰", "〱", "〲", "〳", "〴", "〵", "〶", "〷", "〻", "〼", "〽"]
    "/bdz": ["﹅", "﹆", "﹁", "﹂", "﹃", "﹄", "︙", "︱", "︻", "︼", "︗", "︘", "︵", "︶", "︷", "︸", "︹", "︺", "︿", "﹀", "︽", "︾", "︰", "︲", "︳", "︴", "﹉", "﹊", "﹋", "﹌", "﹍", "﹎", "﹏", "﹇", "﹈", "︐", "︑", "︒", "︔", "︕", "︖"]
    "/bdzy": ["‐", "‑", "‒", "–", "—", "―", "‖", "‗", "‘", "’", "‚", "‛", "“", "”", "„", "‟", "†", "‡", "•", "‣", "․", "‥", "…", "‧", "‰", "‱", "′", "″", "‴", "‵", "‶", "‷", "‸", "‹", "›", "※", "‼", "‽", "‾", "‿", "⁀", "⁁", "⁂", "⁃", "⁄", "⁅", "⁆", "⁇", "⁈", "⁉", "⁊", "⁋", "⁌", "⁍", "⁎", "⁏", "⁐", "⁑", "⁒", "⁓", "⁔", "⁕", "⁖", "⁗", "⁘", "⁙", "⁚", "⁛", "⁜", "⁝", "⁞"]
    "/bg": ["☰", "☱", "☲", "☳", "☴", "☵", "☶", "☷"]
    "/bgm": ["乾", "兑", "离", "震", "巽", "坎", "艮", "坤"]
    "/bh": ["㇀", "㇁", "㇂", "㇃", "㇄", "㇅", "㇆", "㇇", "㇈", "㇉", "㇊", "㇋", "㇌", "㇍", "㇎", "㇏", "㇐", "㇑", "㇒", "㇓", "㇔", "㇕", "㇖", "㇗", "㇘", "㇙", "㇚", "㇛", "㇜", "㇝", "㇞", "㇟", "㇠", "㇡", "㇢", "㇣"]
    "/bq": ["☻", "☺", "☹"]
    "/c": ["ç", "ć", "č", "ĉ", "ċ", "ᶜ", "ȼ", "ꞓ", "ƈ", "ḉ", "ꞔ", "ɕ", "ᶝ", "ꜿ"]
    "/d": ["ď", "ḋ", "ᵈ", "đ", "ƌ", "ᵭ", "ḑ", "ḓ", "ḏ", "ḍ", "ɖ", "ɗ", "ᶑ", "ᶁ", "ð", "ᶞ", "ꝱ", "ʤ", "ʣ", "ʥ", "ȡ", "ƍ", "ǳ", "ǆ", "ẟ"]
    "/db": ["ȸ"]
    "/dh": ["。", "．", "，", "、", "：", "；", "！", "‼", "？", "⁇"]
    "/dn": ["❖", "⌘", "⌃", "⌥", "⎇", "⇧", "⇪", "␣", "⇥", "⇤", "↩", "⌅", "⌤", "⌫", "⌦", "⌧", "⎋", "⌨", "◁", "⌀", "⌖", "⌗", "⏏", "↖", "↘", "⇞", "⇟", "⌚", "⏰", "⏱", "⏲", "⏳", "⌛", "⌜", "⌝⌞⌟", "⍑", "⏩", "⏪", "⏫", "⏬", "⏭", "⏮", "⏯"]
    "/dw": ["Å", "℃", "％", "‰", "‱", "°", "℉", "㏃", "㏆", "㎈", "㏄", "㏅", "㎝", "㎠", "㎤", "㏈", "㎗", "㎙", "㎓", "㎬", "㏉", "㏊", "㏋", "㎐", "㏌", "㎄", "㎅", "㎉", "㎏", "㎑", "㏍", "㎘", "㎞", "㏎", "㎢", "㎦", "㎪", "㏏", "㎸", "㎾", "㏀", "㏐", "㏓", "㎧", "㎨", "㎡", "㎥", "㎃", "㏔", "㎆", "㎎", "㎒", "㏕", "㎖", "㎜", "㎟", "㎣", "㏖", "㎫", "㎳", "㎷", "㎹", "㎽", "㎿", "㏁", "㎁", "㎋", "㎚", "㎱", "㎵", "㎻", "㏘", "㎩", "㎀", "㎊", "㏗", "㏙", "㏚", "㎰", "㎴", "㎺", "㎭", "㎮", "㎯", "㏛", "㏜", "㎔", "㏝", "㎂", "㎌", "㎍", "㎕", "㎛", "㎲", "㎶", "㎼"]
    "/dz": ["子", "丑", "寅", "卯", "辰", "巳", "午", "未", "申", "酉", "戌", "亥"]
    "/e": ["ē", "é", "ě", "è", "ȅ", "ê", "ĕ", "ȇ", "ė", "ë", "ẽ", "ₑ", "ᵉ", "ɇ", "ꬳ", "ẻ", "ȩ", "ę", "ḙ", "ẹ", "ḛ", "ḗ", "ḕ", "ế", "ề", "ễ", "ể", "ḝ", "ệ", "ᶒ", "ꬴ", "ɘ", "ə", "ɚ", "ᶕ", "ɛ", "ᵋ", "ᶓ", "ɜ", "ᵌ", "ᴈ", "ᶟ", "ɝ", "ᶔ", "ɞ", "ʚ", "ǝ", "ₔ", "ᵊ", "ȝ", "ⱸ"]
    "/ey": ["а", "б", "в", "г", "д", "е", "ё", "ж", "з", "и", "й", "к", "л", "м", "н", "о", "п", "р", "с", "т", "у", "ф", "х", "ц", "ч", "ш", "щ", "ъ", "ы", "ь", "э", "ю", "я"]
    "/eyd": ["А", "Б", "В", "Г", "Д", "Е", "Ё", "Ж", "З", "И", "Й", "К", "Л", "М", "Н", "О", "П", "Р", "С", "Т", "У", "Ф", "Х", "Ц", "Ч", "Ш", "Щ", "Ъ", "Ы", "Ь", "Э", "Ю", "Я"]
    "/f": ["ḟ", "ᶠ", "ƒ", "ᵮ", "ᶂ", "ꞙ"]
    "/ff": ["ﬀ"]
    "/ffi": ["ﬃ"]
    "/ffl": ["ﬄ"]
    "/fh": ["©", "®", "℗", "℠", "™", "℡", "℻", "☇", "☈", "☉", "☊", "☋", "☌", "☍", "☎", "☏", "☐", "☑", "☒", "☓", "☕", "☖", "☗", "⛉", "⛊", "☘", "☙", "☚", "☛", "☜", "☝", "☞", "☟", "☠", "☡", "☢", "☣", "☤", "☥", "☦", "☧", "☨", "☩", "☪", "☫", "☬", "☭", "☮", "☯", "☸", "♨", "♰", "♱", "♲", "♳", "♴", "♵", "♶", "♷", "♸", "♹", "♺", "♻", "♼", "♽", "♾", "♿", "⚆", "⚇", "⚈", "⚉", "⚐", "⚑", "⚒", "⚓", "⚔", "⚕", "⚖", "⚗", "⚘", "⚙", "⚚", "⚛", "⚜", "⚝", "⚞", "⚟", "⚠", "⚡", "⚰", "⚱", "⚲", "⚳", "⚴", "⚵", "⚶", "⚷", "⚸", "⚹", "⚺", "⚻", "⚼", "⚽", "⚾", "⚿", "⛀", "⛁", "⛂", "⛃", "⛋", "⛌", "⛍", "⛎", "⛏", "⛐", "⛑", "⛒", "⛓", "⛔", "⛕", "⛖", "⛗", "⛘", "⛙", "⛚", "⛛", "⛜", "⛝", "⛞", "⛟", "⛠", "⛡", "⛢", "⛣", "⛨", "⛩", "⛪", "⛫", "⛬", "⛭", "⛮", "⛯", "⛰", "⛱", "⛲", "⛳", "⛴", "⛵", "⛶", "⛷", "⛸", "⛹", "⛺", "⛻", "⛼", "⛽", "⛾", "⛿"]
    "/fi": ["ﬁ"]
    "/fj": ["⸺", "——", "……", "⋯⋯", "～", "-", "–", "—", "·", "・", "‧", "/", "／", "＼", "｜"]
    "/fk": ["▀", "▁", "▂", "▃", "▄", "▅", "▆", "▇", "█", "▉", "▊", "▋", "▌", "▍", "▎", "▏", "▐", "░", "▒", "▓", "▔", "▕", "▖", "▗", "▘", "▙", "▚", "▛", "▜", "▝", "▞", "▟"]
    "/fl": ["ﬂ"]
    "/fn": ["ʩ"]
    "/fs": ["⅟", "½", "↉", "⅓", "⅔", "¼", "⅕", "⅖", "⅗", "⅘", "⅙", "⅚", "⅐", "⅛", "⅜", "⅝", "⅞", "⅑", "⅒"]
    "/g": ["ḡ", "ǵ", "ǧ", "ĝ", "ğ", "ġ", "ᵍ", "ǥ", "ꞡ", "ģ", "ɠ", "ᵷ", "ᶃ", "ɡ", "ꬶ", "ᶢ", "ɣ", "ˠ", "ɤ", "ᵹ"]
    "/gz": ["甲子", "乙丑", "丙寅", "丁卯", "戊辰", "己巳", "庚午", "辛未", "壬申", "癸酉", "甲戌", "乙亥", "丙子", "丁丑", "戊寅", "己卯", "庚辰", "辛巳", "壬午", "癸未", "甲申", "乙酉", "丙戌", "丁亥", "戊子", "己丑", "庚寅", "辛卯", "壬辰", "癸巳", "甲午", "乙未", "丙申", "丁酉", "戊戌", "己亥", "庚子", "辛丑", "壬寅", "癸卯", "甲辰", "乙巳", "丙午", "丁未", "戊申", "己酉", "庚戌", "辛亥", "壬子", "癸丑", "甲寅", "乙卯", "丙辰", "丁巳", "戊午", "己未", "庚申", "辛酉", "壬戌", "癸亥"]
    "/h": ["ĥ", "ȟ", "ḣ", "ḧ", "ͪ", "ħ", "ɦ", "ʱ", "ꜧ", "ꭜ", "ɧ", "ḩ", "ẖ", "ḫ", "ḥ", "ⱨ", "ꞕ", "ɥ", "ᶣ", "ʮ", "ʯ", "ⱶ"]
    "/hb": ["￥", "¥", "¤", "￠", "＄", "$", "￡", "£", "৳", "฿", "₠", "₡", "₢", "₣", "₤", "₥", "₦", "₧", "₩", "₪", "₫", "€", "₭", "₮", "₯", "₰", "₱", "₲", "₳", "₴", "₵", "₶", "₷", "₸", "₹", "₺", "₨", "﷼"]
    "/hj": ["＿", "﹏", "●", "•"]
    "/hv": ["ƕ"]
    "/hw": ["ㄱ", "ㄴ", "ㄷ", "ㄹ", "ㅁ", "ㅂ", "ㅅ", "ㅇ", "ㅈ", "ㅊ", "ㅋ", "ㅌ", "ㅍ", "ㅎ"]
    "/hwh": ["㈀", "㈁", "㈂", "㈃", "㈄", "㈅", "㈆", "㈇", "㈈", "㈉", "㈊", "㈋", "㈌", "㈍", "㈎", "㈏", "㈐", "㈑", "㈒", "㈓", "㈔", "㈕", "㈖", "㈗", "㈘", "㈙", "㈚", "㈛", "㈜", "㈝", "㈞"]
    "/hwq": ["㉠", "㉡", "㉢", "㉣", "㉤", "㉥", "㉦", "㉧", "㉨", "㉩", "㉪", "㉫", "㉬", "㉭", "㉮", "㉯", "㉰", "㉱", "㉲", "㉳", "㉴", "㉵", "㉶", "㉷", "㉸", "㉹", "㉺", "㉻", "㉼", "㉽", "㉾", "㉿"]
    "/hzh": ["㈠", "㈡", "㈢", "㈣", "㈤", "㈥", "㈦", "㈧", "㈨", "㈩", "㈪", "㈫", "㈬", "㈭", "㈮", "㈯", "㈰", "㈱", "㈲", "㈳", "㈴", "㈵", "㈶", "㈷", "㈸", "㈹", "㈺", "㈻", "㈼", "㈽", "㈾", "㈿", "㉀", "㉁", "㉂", "㉃"]
    "/hzq": ["㊀", "㊁", "㊂", "㊃", "㊄", "㊅", "㊆", "㊇", "㊈", "㊉", "㊊", "㊋", "㊌", "㊍", "㊎", "㊏", "㊐", "㊑", "㊒", "㊓", "㊔", "㊕", "㊖", "㊗", "㊘", "㊙", "㊚", "㊛", "㊜", "㊝", "㊞", "㊟", "㊠", "㊡", "㊢", "㊣", "㊤", "㊥", "㊦", "㊧", "㊨", "㊩", "㊪", "㊫", "㊬", "㊭", "㊮", "㊯", "㊰", "㉄", "㉅", "㉆", "㉇"]
    "/i": ["ī", "í", "ǐ", "ì", "ȉ", "î", "ĭ", "ȋ", "ï", "ĩ", "ᵢ", "ı", "ɨ", "ᶤ", "ỉ", "į", "ị", "ḭ", "ᴉ", "ᵎ", "ḯ", "ᶖ", "ɩ", "ᶥ", "ᵼ"]
    "/ij": ["ĳ"]
    "/iro": ["い", "ろ", "は", "に", "ほ", "へ", "と", "ち", "り", "ぬ", "る", "を", "わ", "か", "よ", "た", "れ", "そ", "つ", "ね", "な", "ら", "む", "う", "ゐ", "の", "お", "く", "や", "ま", "け", "ふ", "こ", "え", "て", "あ", "さ", "き", "ゆ", "め", "み", "し", "ゑ", "ひ", "も", "せ", "す"]
    "/j": ["ĵ", "ǰ", "ⱼ", "ʲ", "ɉ", "ȷ", "ɟ", "ᶡ", "ʄ", "ʝ", "ᶨ"]
    "/jg": ["⿰", "⿱", "⿲", "⿳", "⿴", "⿵", "⿶", "⿷", "⿸", "⿹", "⿺", "⿻", "〾"]
    "/jh": ["■", "□", "▢", "▣", "▤", "▥", "▦", "▧", "▨", "▩", "▪", "▫", "▬", "▭", "▮", "▯", "▰", "▱", "▲", "△", "▴", "▵", "▶", "▷", "▸", "▹", "►", "▻", "▼", "▽", "▾", "▿", "◀", "◁", "◂", "◃", "◄", "◅", "◆", "◇", "◈", "◉", "◊", "○", "◌", "◍", "◎", "●", "◐", "◑", "◒", "◓", "◔", "◕", "◖", "◗", "◘", "◙", "◚", "◛", "◜", "◝", "◞", "◟", "◠", "◡", "◢", "◣", "◤", "◥", "◦", "◧", "◨", "◩", "◪", "◫", "◬", "◭", "◮", "◯", "◰", "◱", "◲", "◳", "◴", "◵", "◶", "◷", "◸", "◹", "◺", "◻", "◼", "◽", "◾", "◿"]
    "/jm": ["あ", "ぁ", "い", "ぃ", "う", "ぅ", "え", "ぇ", "お", "ぉ", "か", "ゕ", "が", "き", "ぎ", "く", "ぐ", "け", "ゖ", "げ", "こ", "ご", "さ", "ざ", "し", "じ", "す", "ず", "せ", "ぜ", "そ", "ぞ", "た", "だ", "ち", "ぢ", "つ", "っ", "づ", "て", "で", "と", "ど", "な", "に", "ぬ", "ね", "の", "は", "ば", "ぱ", "ひ", "び", "ぴ", "ふ", "ぶ", "ぷ", "へ", "べ", "ぺ", "ほ", "ぼ", "ぽ", "ま", "み", "む", "め", "も", "や", "ゃ", "ゆ", "ゅ", "よ", "ょ", "ら", "り", "る", "れ", "ろ", "わ", "ゎ", "ゐ", "ゔ", "ゑ", "を", "ん", "・", "ー", "ゝ", "ゞ", "ゟ"]
    "/jma": ["あ", "か", "が", "さ", "ざ", "た", "だ", "な", "は", "ば", "ぱ", "ま", "や", "ら", "わ", "ア", "カ", "ガ", "サ", "ザ", "タ", "ダ", "ナ", "ハ", "バ", "パ", "マ", "ヤ", "ラ", "ワ"]
    "/jmb": ["ば", "び", "ぶ", "べ", "ぼ", "バ", "ビ", "ブ", "ベ", "ボ"]
    "/jmbj": ["ｱ", "ｧ", "ｲ", "ｨ", "ｳ", "ｩ", "ｴ", "ｪ", "ｵ", "ｫ", "ｶ", "ｷ", "ｸ", "ｹ", "ｺ", "ｻ", "ｼ", "ｽ", "ｾ", "ｿ", "ﾀ", "ﾁ", "ﾂ", "ｯ", "ﾃ", "ﾄ", "ﾅ", "ﾆ", "ﾇ", "ﾈ", "ﾉ", "ﾊ", "ﾋ", "ﾌ", "ﾍ", "ﾎ", "ﾏ", "ﾐ", "ﾑ", "ﾒ", "ﾓ", "ﾔ", "ｬ", "ﾕ", "ｭ", "ﾖ", "ｮ", "ﾗ", "ﾘ", "ﾙ", "ﾚ", "ﾛ", "ﾜ", "ｦ", "ﾝ", "･", "ｰ", "ﾞ", "ﾟ"]
    "/jmd": ["だ", "ぢ", "づ", "で", "ど", "ダ", "ヂ", "ヅ", "デ", "ド"]
    "/jme": ["え", "け", "げ", "せ", "ぜ", "て", "で", "ね", "へ", "べ", "ぺ", "め", "れ", "ゑ", "エ", "ケ", "ゲ", "セ", "ゼ", "テ", "デ", "ネ", "ヘ", "ベ", "ペ", "メ", "レ", "ヱ"]
    "/jmg": ["が", "ぎ", "ぐ", "げ", "ご", "ガ", "ギ", "グ", "ゲ", "ゴ"]
    "/jmh": ["は", "ひ", "ふ", "へ", "ほ", "ハ", "ヒ", "フ", "ヘ", "ホ"]
    "/jmi": ["い", "き", "ぎ", "し", "じ", "ち", "ぢ", "に", "ひ", "び", "ぴ", "み", "り", "ゐ", "イ", "キ", "ギ", "シ", "ジ", "チ", "ヂ", "ニ", "ヒ", "ビ", "ピ", "ミ", "リ", "ヰ"]
    "/jmk": ["か", "ゕ", "き", "く", "け", "ゖ", "こ", "カ", "ヵ", "キ", "ク", "ケ", "ヶ", "コ"]
    "/jmm": ["ま", "み", "む", "め", "も", "マ", "ミ", "ム", "メ", "モ"]
    "/jmn": ["な", "に", "ぬ", "ね", "の", "ん", "ナ", "ニ", "ヌ", "ネ", "ノ", "ン"]
    "/jmo": ["お", "こ", "ご", "そ", "ぞ", "と", "ど", "の", "ほ", "ぼ", "ぽ", "も", "ろ", "を", "オ", "コ", "ゴ", "ソ", "ゾ", "ト", "ド", "ノ", "ホ", "ボ", "ポ", "モ", "ロ", "ヲ"]
    "/jmp": ["ぱ", "ぴ", "ぷ", "ぺ", "ぽ", "パ", "ピ", "プ", "ペ", "ポ"]
    "/jmq": ["㋐", "㋑", "㋒", "㋓", "㋔", "㋕", "㋖", "㋗", "㋘", "㋙", "㋚", "㋛", "㋜", "㋝", "㋞", "㋟", "㋠", "㋡", "㋢", "㋣", "㋤", "㋥", "㋦", "㋧", "㋨", "㋩", "㋪", "㋫", "㋬", "㋭", "㋮", "㋯", "㋰", "㋱", "㋲", "㋳", "㋴", "㋵", "㋶", "㋷", "㋸", "㋹", "㋺", "㋻", "㋼", "㋽", "㋾"]
    "/jmr": ["ら", "り", "る", "れ", "ろ", "ラ", "リ", "ル", "レ", "ロ"]
    "/jms": ["さ", "し", "す", "せ", "そ", "サ", "シ", "ス", "セ", "ソ"]
    "/jmt": ["た", "ち", "つ", "っ", "て", "と", "タ", "チ", "ツ", "ッ", "テ", "ト"]
    "/jmu": ["う", "く", "ぐ", "す", "ず", "つ", "づ", "ぬ", "ふ", "ぶ", "ぷ", "む", "る", "ウ", "ク", "グ", "ス", "ズ", "ツ", "ヅ", "ヌ", "フ", "ブ", "プ", "ム", "ル"]
    "/jmw": ["わ", "ゐ", "ゑ", "を", "ワ", "ヰ", "ヱ", "ヲ"]
    "/jmy": ["や", "ゃ", "ゆ", "ゅ", "よ", "ょ", "ヤ", "ャ", "ユ", "ュ", "ヨ", "ョ"]
    "/jmz": ["ざ", "じ", "ず", "ぜ", "ぞ", "ザ", "ジ", "ズ", "ゼ", "ゾ"]
    "/jq": ["立春", "雨水", "惊蛰", "春分", "清明", "谷雨", "立夏", "小满", "芒种", "夏至", "小暑", "大暑", "立秋", "处暑", "白露", "秋分", "寒露", "霜降", "立冬", "小雪", "大雪", "冬至", "小寒", "大寒"]
    "/jt": ["↑", "↓", "←", "→", "↕", "↔", "↖", "↗", "↙", "↘", "↚", "↛", "↮", "↜", "↝", "↞", "↟", "↠", "↡", "↢", "↣", "↤", "↥", "↦", "↧", "↨", "↩", "↪", "↫", "↬", "↭", "↯", "↰", "↱", "↲", "↳", "↴", "↵", "↶", "↷", "↸", "↹", "↺", "↻", "↼", "↽", "↾", "↿", "⇀", "⇁", "⇂", "⇃", "⇄", "⇅", "⇆", "⇇", "⇈", "⇉", "⇊", "⇋", "⇌", "⇐", "⇍", "⇑", "⇒", "⇏", "⇓", "⇔", "⇎", "⇕", "⇖", "⇗", "⇘", "⇙", "⇚", "⇛", "⇜", "⇝", "⇞", "⇟", "⇠", "⇡", "⇢", "⇣", "⇤", "⇥", "⇦", "⇧", "⇨", "⇩", "⇪", "⇫", "⇬", "⇭", "⇮", "⇯", "⇰", "⇱", "⇲", "⇳", "⇴", "⇵", "⇶", "⇷", "⇸", "⇹", "⇺", "⇻", "⇼", "⇽", "➔", "➘", "➙", "➚", "➛", "➜", "➝", "➞", "➟", "➠", "➡", "➢", "➣", "➤", "➥", "➦", "➧", "➨", "➩", "➪", "➫", "➬", "➭", "➮", "➱", "➲", "➳", "➴", "➵", "➶", "➷", "➸", "➹", "➺", "➻", "➼", "➽", "➾"]
    "/jz": ["「", "」", "『", "』", "“", "”", "‘", "’", "（", "）", "《", "》", "〈", "〉", "【", "】", "〖", "〗", "〔", "〕", "［", "］", "｛", "｝", "«", "»", "‹", "›", "⟨", "⟩"]
    "/k": ["ḱ", "ǩ", "ₖ", "ᵏ", "ꝁ", "ꝃ", "ꞣ", "ꝅ", "ķ", "ḵ", "ḳ", "ƙ", "ᶄ", "ⱪ", "ʞ", "ĸ"]
    "/kx": ["一", "丨", "丶", "丿", "乙", "亅", "二", "亠", "人", "儿", "入", "八", "冂", "冖", "冫", "几", "凵", "刀", "力", "勹", "匕", "匚", "匸", "十", "卜", "卩", "厂", "厶", "又", "口", "囗", "土", "士", "夂", "夊", "夕", "大", "女", "子", "宀", "寸", "小", "尢", "尸", "屮", "山", "巛", "工", "己", "巾", "干", "幺", "广", "廴", "廾", "弋", "弓", "彐", "彡", "彳", "心", "戈", "戶", "手", "支", "攴", "文", "斗", "斤", "方", "无", "日", "曰", "月", "木", "欠", "止", "歹", "殳", "毋", "比", "毛", "氏", "气", "水", "火", "爪", "父", "爻", "爿", "片", "牙", "牛", "犬", "玄", "玉", "瓜", "瓦", "甘", "生", "用", "田", "疋", "疒", "癶", "白", "皮", "皿", "目", "矛", "矢", "石", "示", "禸", "禾", "穴", "立", "竹", "米", "糸", "缶", "网", "羊", "羽", "老", "而", "耒", "耳", "聿", "肉", "臣", "自", "至", "臼", "舌", "舛", "舟", "艮", "色", "艸", "虍", "虫", "血", "行", "衣", "襾", "見", "角", "言", "谷", "豆", "豕", "豸", "貝", "赤", "走", "足", "身", "車", "辛", "辰", "辵", "邑", "酉", "釆", "里", "金", "長", "門", "阜", "隶", "隹", "雨", "靑", "非", "面", "革", "韋", "韭", "音", "頁", "風", "飛", "食", "首", "香", "馬", "骨", "高", "髟", "鬥", "鬯", "鬲", "鬼", "魚", "鳥", "鹵", "鹿", "麥", "麻", "黃", "黍", "黑", "黹", "黽", "鼎", "鼓", "鼠", "鼻", "齊", "齒", "龍", "龜", "龠"]
    "/l": ["ĺ", "ˡ", "ł", "ꝉ", "ƚ", "ⱡ", "ɫ", "ꭞ", "ꬸ", "ɬ", "ľ", "ļ", "ḻ", "ḽ", "ḷ", "ŀ", "ꝲ", "ƛ", "ᶅ", "ᶪ", "ɭ", "ᶩ", "ḹ", "ꬷ", "ꭝ", "ꬹ", "ȴ", "ꝇ"]
    "/lj": ["ǉ"]
    "/ll": ["ỻ"]
    "/lm": ["ⅰ", "ⅱ", "ⅲ", "ⅳ", "ⅴ", "ⅵ", "ⅶ", "ⅷ", "ⅸ", "ⅹ", "ⅺ", "ⅻ", "ⅼ", "ⅽ", "ⅾ", "ⅿ"]
    "/lmd": ["Ⅰ", "Ⅱ", "Ⅲ", "Ⅳ", "Ⅴ", "Ⅵ", "Ⅶ", "Ⅷ", "Ⅸ", "Ⅹ", "Ⅺ", "Ⅻ", "Ⅼ", "Ⅽ", "Ⅾ", "Ⅿ"]
    "/ls": ["ʪ"]
    "/lssg": ["䷀", "䷁", "䷂", "䷃", "䷄", "䷅", "䷆", "䷇", "䷈", "䷉", "䷊", "䷋", "䷌", "䷍", "䷎", "䷏", "䷐", "䷑", "䷒", "䷓", "䷔", "䷕", "䷖", "䷗", "䷘", "䷙", "䷚", "䷛", "䷜", "䷝", "䷞", "䷟", "䷠", "䷡", "䷢", "䷣", "䷤", "䷥", "䷦", "䷧", "䷨", "䷩", "䷪", "䷫", "䷬", "䷭", "䷮", "䷯", "䷰", "䷱", "䷲", "䷳", "䷴", "䷵", "䷶", "䷷", "䷸", "䷹", "䷺", "䷻", "䷼", "䷽", "䷾", "䷿"]
    "/lssgm": ["乾", "坤", "屯", "蒙", "需", "讼", "师", "比", "小畜", "履", "泰", "否", "同人", "大有", "谦", "豫", "随", "蛊", "临", "观", "噬嗑", "贲", "剥", "复", "无妄", "大畜", "颐", "大过", "坎", "离", "咸", "恒", "遯", "大壮", "晋", "明夷", "家人", "睽", "蹇", "解", "损", "益", "夬", "姤", "萃", "升", "困", "井", "革", "鼎", "震", "艮", "渐", "归妹", "丰", "旅", "巽", "兑", "涣", "节", "中孚", "小过", "既济", "未济"]
    "/lx": ["♂", "♀", "⚢", "⚣", "⚤", "⚥", "⚦", "⚧", "⚨", "⚩", "⚪", "⚫", "⚬", "⚭", "⚮", "⚯"]
    "/lz": ["ʫ", "ɮ"]
    "/m": ["ḿ", "ṁ", "ᵐ", "ₘ", "ṃ", "ᵯ", "ɱ", "ᶬ", "ꬺ", "ᶆ", "ꝳ", "ɯ", "ᵚ", "ɰ", "ᶭ", "ᴟ"]
    "/mj": ["🀀", "🀁", "🀂", "🀃", "🀄", "🀅", "🀆", "🀇", "🀈", "🀉", "🀊", "🀋", "🀌", "🀍", "🀎", "🀏", "🀐", "🀑", "🀒", "🀓", "🀔", "🀕", "🀖", "🀗", "🀘", "🀙", "🀚", "🀛", "🀜", "🀝", "🀞", "🀟", "🀠", "🀡", "🀢", "🀣", "🀤", "🀥", "🀦", "🀧", "🀨", "🀩", "🀪", "🀫"]
    "/n": ["ń", "ň", "ǹ", "ṅ", "ñ", "ₙ", "ⁿ", "ɲ", "ᶮ", "ɳ", "ᶯ", "ȵ", "ƞ", "ŋ", "ᵑ", "ꬻ", "ꬼ", "ꝴ", "ŉ", "ꞥ", "ņ", "ṉ", "ṋ", "ṇ", "ᵰ", "ꞑ", "ᶇ"]
    "/nj": ["ǌ"]
    "/num": ["№"]
    "/o": ["ō", "ó", "ǒ", "ò", "ő", "ô", "ŏ", "ȯ", "ö", "õ", "ₒ", "ᵒ", "º", "ɔ", "ᵓ", "ᶗ", "ꬿ", "ø", "ǫ", "ọ", "ơ", "ɵ", "ᶱ", "ᴑ", "ᴒ", "ᴓ", "ꝋ", "ꝍ", "ṓ", "ṑ", "ố", "ồ", "ỗ", "ổ", "ȱ", "ȫ", "ȭ", "ṍ", "ṏ", "ộ", "ǭ", "ǿ", "ớ", "ờ", "ỡ", "ở", "ợ", "ɷ", "ⱺ", "ᴖ", "ᵔ", "ᴗ", "ᵕ"]
    "/oe": ["œ", "ᴔ"]
    "/oi": ["ƣ"]
    "/oo": ["ꝏ"]
    "/ou": ["ȣ"]
    "/p": ["ṕ", "ṗ", "ᵖ", "ᵽ", "ꝑ", "ᵱ", "ƥ", "ᶈ", "ꝓ", "ꝕ", "ɸ", "ᶲ", "ⱷ"]
    "/pjm": ["ア", "ァ", "イ", "ィ", "ウ", "ゥ", "エ", "ェ", "オ", "ォ", "カ", "ヵ", "ガ", "キ", "ギ", "ク", "グ", "ケ", "ヶ", "ゲ", "コ", "ゴ", "サ", "ザ", "シ", "ジ", "ス", "ズ", "セ", "ゼ", "ソ", "ゾ", "タ", "ダ", "チ", "ヂ", "ツ", "ッ", "ヅ", "テ", "デ", "ト", "ド", "ナ", "ニ", "ヌ", "ネ", "ノ", "ハ", "バ", "パ", "ヒ", "ビ", "ピ", "フ", "ブ", "プ", "ヘ", "ベ", "ペ", "ホ", "ボ", "ポ", "マ", "ミ", "ム", "メ", "モ", "ヤ", "ャ", "ユ", "ュ", "ヨ", "ョ", "ラ", "リ", "ル", "レ", "ロ", "ワ", "ヮ", "ヰ", "ヸ", "ヴ", "ヱ", "ヹ", "ヲ", "ヺ", "ン", "・", "ー", "ヽ", "ヾ", "ヿ", "ㇰ", "ㇱ", "ㇲ", "ㇳ", "ㇴ", "ㇵ", "ㇶ", "ㇷ", "ㇸ", "ㇹ", "ㇺ", "ㇻ", "ㇼ", "ㇽ", "ㇾ", "ㇿ"]
    "/pk": ["♠", "♥", "♣", "♦", "♤", "♡", "♧", "♢"]
    "/pp": ["乛", "冫", "丷", "龹", "⺌", "龸", "亻", "亼", "亽", "仒", "冖", "冂", "冃", "冄", "宀", "罒", "㓁", "罓", "冈", "凵", "厶", "刂", "勹", "匚", "匸", "卩", "阝", "厂", "丆", "广", "壬", "訁", "讠", "釒", "钅", "飠", "饣", "龺", "攵", "夂", "夊", "尢", "尣", "兂", "旡", "巜", "巛", "彐", "彑", "彡", "彳", "龰", "辶", "廴", "㞢", "忄", "㣺", "扌", "爫", "龵", "廾", "歺", "癶", "氵", "氺", "火", "灬", "爿", "丬", "疒", "牜", "⺶", "犭", "豕", "豸", "虍", "艹", "卝", "龷", "丗", "龶", "芈", "丵", "菐", "黹", "礻", "衤", "糸", "糹", "纟", "龻", "镸", "髟", "襾", "覀", "吅", "㗊", "㠭", "㸚", "叕"]
    "/py": ["ā", "á", "ǎ", "à", "ō", "ó", "ǒ", "ò", "ê", "ê̄", "ế", "ê̌", "ề", "ē", "é", "ě", "è", "ī", "í", "ǐ", "ì", "ū", "ú", "ǔ", "ù", "ü", "ǖ", "ǘ", "ǚ", "ǜ", "ḿ", "m̀", "ń", "ň", "ǹ", "ẑ", "ĉ", "ŝ", "ŋ"]
    "/pyd": ["Ā", "Á", "Ǎ", "À", "Ō", "Ó", "Ǒ", "Ò", "Ê", "Ê̄", "Ế", "Ê̌", "Ề", "Ē", "É", "Ě", "È", "Ī", "Í", "Ǐ", "Ì", "Ū", "Ú", "Ǔ", "Ù", "Ü", "Ǖ", "Ǘ", "Ǚ", "Ǜ", "Ḿ", "M̀", "Ń", "Ň", "Ǹ", "Ẑ", "Ĉ", "Ŝ", "Ŋ"]
    "/q": ["ɋ", "ꝗ", "ꝙ", "ʠ"]
    "/qp": ["ȹ"]
    "/r": ["ŕ", "ř", "ȑ", "ȓ", "ṙ", "ᵣ", "ɍ", "ꞧ", "ᵲ", "ŗ", "ṟ", "ṛ", "ṝ", "ᵳ", "ɽ", "ᶉ", "ꭇ", "ꭈ", "ꭊ", "ꭉ", "ꝵ", "ꭋ", "ꭌ", "ɹ", "ʴ", "ɺ", "ɻ", "ʵ", "ⱹ", "ɼ", "ʳ", "ɾ", "ɿ", "ꝛ", "ꝝ"]
    "/rq": ["㏠", "㏡", "㏢", "㏣", "㏤", "㏥", "㏦", "㏧", "㏨", "㏩", "㏪", "㏫", "㏬", "㏭", "㏮", "㏯", "㏰", "㏱", "㏲", "㏳", "㏴", "㏵", "㏶", "㏷", "㏸", "㏹", "㏺", "㏻", "㏼", "㏽", "㏾"]
    "/s": ["/s.", "🆚", "ś", "ŝ", "š", "ṡ", "ˢ", "ʂ", "ᶳ", "ᵴ", "ꞩ", "ᶊ", "ş", "ṣ", "ș", "ȿ", "ṥ", "ṧ", "ṩ", "ʃ", "ᶴ", "ʆ", "ᶘ", "ʅ", "ƪ", "ß", "ſ", "ẛ", "ẜ", "ẝ"]
    "/sb": ["⁰", "¹", "²", "³", "⁴", "⁵", "⁶", "⁷", "⁸", "⁹", "˜", "⁺", "⁻", "⁼", "⁽", "⁾", "ᴬ", "ᵃ", "ᵄ", "ᵅ", "ᶛ", "ᴭ", "ᵆ", "ᴮ", "ᴯ", "ᵇ", "ᵝ", "ᶜ", "ᵓ", "ᶝ", "ᴰ", "ᵈ", "ᶞ", "ᵟ", "ᴱ", "ᵉ", "ᴲ", "ᵊ", "ᵋ", "ᶟ", "ᵌ", "ᶠ", "ᶡ", "ᶲ", "ᵠ", "ᴳ", "ᵍ", "ᶢ", "ˠ", "ᵞ", "ᴴ", "ʰ", "ᶣ", "ʱ", "ᴵ", "ⁱ", "ᶤ", "ᵎ", "ᶥ", "ᴶ", "ʲ", "ᶨ", "ᴷ", "ᵏ", "ᴸ", "ᶫ", "ˡ", "ᶩ", "ᶪ", "ᴹ", "ᵐ", "ᶬ", "ᵚ", "ᶭ", "ᴺ", "ᴻ", "ⁿ", "ᵑ", "ᶮ", "ᶯ", "ᴼ", "ᵒ", "ᶱ", "ᴽ", "ᴾ", "ᵖ", "ᴿ", "ʳ", "ʶ", "ʴ", "ʵ", "ˢ", "ᶴ", "ᶳ", "ᵀ", "ᵗ", "ᶵ", "ᶿ", "ᵁ", "ᵘ", "ᶶ", "ᶷ", "ᵙ", "ⱽ", "ᵛ", "ᶺ", "ᶹ", "ᵂ", "ʷ", "ˣ", "ᵡ", "ʸ", "ᶻ", "ᶾ", "ᶽ", "ᶼ"]
    "/sd": ["ˉ", "ˊ", "ˇ", "ˋ", "ˆ", "˙", "˜", "˥", "˦", "˧", "˨", "˩", "꜀", "꜁", "꜂", "꜃", "꜄", "꜅", "꜆", "꜇", "〪", "〫", "〬", "〭"]
    "/sj": ["㍘", "㍙", "㍚", "㍛", "㍜", "㍝", "㍞", "㍟", "㍠", "㍡", "㍢", "㍣", "㍤", "㍥", "㍦", "㍧", "㍨", "㍩", "㍪", "㍫", "㍬", "㍭", "㍮", "㍯", "㍰"]
    "/sx": ["±", "÷", "×", "∈", "∏", "∑", "－", "＋", "＜", "≮", "＝", "≠", "＞", "≯", "∕", "√", "∝", "∞", "∟", "∠", "∥", "∧", "∨", "∩", "∪", "∫", "∮", "∴", "∵", "∷", "∽", "≈", "≌", "≒", "≡", "≤", "≥", "≦", "≧", "⊕", "⊙", "⊥", "⊿", "㏑", "㏒"]
    "/sz": ["⚀", "⚁", "⚂", "⚃", "⚄", "⚅"]
    "/szd": ["⒈", "⒉", "⒊", "⒋", "⒌", "⒍", "⒎", "⒏", "⒐", "⒑", "⒒", "⒓", "⒔", "⒕", "⒖", "⒗", "⒘", "⒙", "⒚", "⒛"]
    "/szh": ["⑴", "⑵", "⑶", "⑷", "⑸", "⑹", "⑺", "⑻", "⑼", "⑽", "⑾", "⑿", "⒀", "⒁", "⒂", "⒃", "⒄", "⒅", "⒆", "⒇"]
    "/szm": ["〡", "〢", "〣", "〤", "〥", "〦", "〧", "〨", "〩", "〸", "〹", "〺"]
    "/szq": ["⓪", "①", "②", "③", "④", "⑤", "⑥", "⑦", "⑧", "⑨", "⑩", "⑪", "⑫", "⑬", "⑭", "⑮", "⑯", "⑰", "⑱", "⑲", "⑳", "㉑", "㉒", "㉓", "㉔", "㉕", "㉖", "㉗", "㉘", "㉙", "㉚", "㉛", "㉜", "㉝", "㉞", "㉟", "㊱", "㊲", "㊳", "㊴", "㊵", "㊶", "㊷", "㊸", "㊹", "㊺", "㊻", "㊼", "㊽", "㊾", "㊿", "⓿", "❶", "❷", "❸", "❹", "❺", "❻", "❼", "❽", "❾", "❿", "⓫", "⓬", "⓭", "⓮", "⓯", "⓰", "⓱", "⓲", "⓳", "⓴"]
    "/t": ["ť", "ṫ", "ẗ", "ᵗ", "ₜ", "ʈ", "þ", "ꝥ", "ꝧ", "ŧ", "ⱦ", "ţ", "ṯ", "ṱ", "ṭ", "ț", "ƭ", "ᵵ", "ƫ", "ᶵ", "ʇ", "ȶ", "ꝷ"]
    "/tc": ["ʨ"]
    "/tg": ["甲", "乙", "丙", "丁", "戊", "己", "庚", "辛", "壬", "癸"]
    "/th": ["ᵺ"]
    "/tq": ["☀", "☁", "⛅", "⛈", "⛆", "☂", "☔", "☃", "⛄", "⛇"]
    "/ts": ["ʦ", "ʧ"]
    "/tt": ["☄", "☼", "☽", "☾", "☿", "♀", "♁", "♂", "♃", "♄", "♅", "♆", "♇"]
    "/txj": ["⚊", "⚋", "⚌", "⚍", "⚎", "⚏", "𝌀", "𝌁", "𝌂", "𝌃", "𝌄", "𝌅", "𝌆", "𝌇", "𝌈", "𝌉", "𝌊", "𝌋", "𝌌", "𝌍", "𝌎", "𝌏", "𝌐", "𝌑", "𝌒", "𝌓", "𝌔", "𝌕", "𝌖", "𝌗", "𝌘", "𝌙", "𝌚", "𝌛", "𝌜", "𝌝", "𝌞", "𝌟", "𝌠", "𝌡", "𝌢", "𝌣", "𝌤", "𝌥", "𝌦", "𝌧", "𝌨", "𝌩", "𝌪", "𝌫", "𝌬", "𝌭", "𝌮", "𝌯", "𝌰", "𝌱", "𝌲", "𝌳", "𝌴", "𝌵", "𝌶", "𝌷", "𝌸", "𝌹", "𝌺", "𝌻", "𝌼", "𝌽", "𝌾", "𝌿", "𝍀", "𝍁", "𝍂", "𝍃", "𝍄", "𝍅", "𝍆", "𝍇", "𝍈", "𝍉", "𝍊", "𝍋", "𝍌", "𝍍", "𝍎", "𝍏", "𝍐", "𝍑", "𝍒", "𝍓", "𝍔", "𝍕", "𝍖"]
    "/tz": ["ꜩ"]
    "/u": ["ū", "ú", "ǔ", "ù", "ű", "ȕ", "û", "ŭ", "ȗ", "ü", "ǖ", "ǘ", "ǚ", "ǜ", "ů", "ũ", "ᵤ", "ᵘ", "ʉ", "ᶶ", "ủ", "ų", "ṷ", "ụ", "ṳ", "ṵ", "ư", "ʊ", "ᶷ", "ᵿ", "ᶙ", "ṻ", "ṹ", "ứ", "ừ", "ữ", "ử", "ự", "ꭒ", "ꭟ", "ꝸ", "ꭎ", "ꭏ", "ᴝ", "ᵙ", "ᴞ"]
    "/ue": ["ᵫ"]
    "/v": ["ü", "ǖ", "ǘ", "ǚ", "ǜ", "ṽ", "ᵛ", "ᵥ", "ṿ", "ꝟ", "ʋ", "ᶹ", "ᶌ", "ⱴ", "ⱱ", "ỽ", "ʌ", "ᶺ"]
    "/vy": ["ꝡ"]
    "/w": ["ẃ", "ẁ", "ŵ", "ẇ", "ẅ", "ẘ", "ʷ", "ẉ", "ƿ", "ʍ", "ⱳ"]
    "/ww": ["ʬ"]
    "/x": ["ẋ", "ẍ", "ᶍ", "ˣ", "ₓ", "ꭖ", "ꭗ", "ꭘ", "ꭙ"]
    "/xb": ["₀", "₁", "₂", "₃", "₄", "₅", "₆", "₇", "₈", "₉", "₊", "₋", "₌", "₍", "₎", "‸", "ᴀ", "ₐ", "ᴁ", "ʙ", "ᴃ", "ᵦ", "ᴄ", "ᴐ", "ᴒ", "ᴅ", "ᴆ", "ᴇ", "ₑ", "ₔ", "ᵩ", "ɢ", "ʛ", "ᴦ", "ᵧ", "ʜ", "ₕ", "ɪ", "ᵻ", "ᵢ", "ᴊ", "ⱼ", "ᴋ", "ₖ", "ʟ", "ₗ", "ᴌ", "ᴧ", "ᴍ", "ₘ", "ꟺ", "ɴ", "ᴎ", "ₙ", "ᴏ", "ₒ", "ɶ", "ʘ", "ᴓ", "ᴑ", "ᴘ", "ₚ", "ᴨ", "ᴪ", "ʀ", "ᵣ", "ᴙ", "ʁ", "ᴚ", "ᵨ", "ₛ", "ᴛ", "ₜ", "ᴜ", "ᵤ", "ᵾ", "ᴠ", "ᵥ", "ᴡ", "ₓ", "ᵪ", "ʏ", "ᴢ", "ᴣ"]
    "/xh": ["★", "☆", "⛤", "⛥", "⛦", "⛧", "✡", "❋", "❊", "❉", "❈", "❇", "❆", "❅", "❄", "❃", "❂", "❁", "❀", "✿", "✾", "✽", "✼", "✻", "✺", "✹", "✸", "✷", "✶", "✵", "✴", "✳", "✲", "✱", "✰", "✯", "✮", "✭", "✬", "✫", "✪", "✩", "✧", "✦", "✥", "✤", "✣", "✢"]
    "/xl": ["α", "β", "γ", "δ", "ε", "ζ", "η", "θ", "ι", "κ", "λ", "μ", "ν", "ξ", "ο", "π", "ρ", "σ", "τ", "υ", "φ", "χ", "ψ", "ω"]
    "/xld": ["Α", "Β", "Γ", "Δ", "Ε", "Ζ", "Η", "Θ", "Ι", "Κ", "Λ", "Μ", "Ν", "Ξ", "Ο", "Π", "Ρ", "Σ", "Τ", "Υ", "Φ", "Χ", "Ψ", "Ω"]
    "/xq": ["♔", "♕", "♖", "♗", "♘", "♙", "♚", "♛", "♜", "♝", "♞", "♟"]
    "/xz": ["♈", "♉", "♊", "♋", "♌", "♍", "♎", "♏", "♐", "♑", "♒", "♓"]
    "/xzg": ["白羊宫", "金牛宫", "双子宫", "巨蟹宫", "狮子宫", "室女宫", "天秤宫", "天蝎宫", "人马宫", "摩羯宫", "宝瓶宫", "双鱼宫"]
    "/xzm": ["白羊座", "金牛座", "双子座", "巨蟹座", "狮子座", "室女座", "天秤座", "天蝎座", "人马座", "摩羯座", "宝瓶座", "双鱼座"]
    "/y": ["ȳ", "ý", "ỳ", "ŷ", "ẏ", "ÿ", "ẙ", "ỹ", "ʸ", "ɏ", "ỷ", "ỵ", "ƴ", "ʎ", "ỿ", "ꭚ"]
    "/yf": ["㋀", "㋁", "㋂", "㋃", "㋄", "㋅", "㋆", "㋇", "㋈", "㋉", "㋊", "㋋"]
    "/yr": ["月", "火", "水", "木", "金", "土", "日", "㊊", "㊋", "㊌", "㊍", "㊎", "㊏", "㊐", "㊗", "㊡", "㈪", "㈫", "㈬", "㈭", "㈮", "㈯", "㈰", "㈷", "㉁", "㉀"]
    "/yy": ["𝄞", "♩", "♪", "♫", "♬", "♭", "♮", "♯"]
    "/z": ["ź", "ž", "ẑ", "ż", "ᶻ", "ʐ", "ᶼ", "ʑ", "ᶽ", "ƶ", "ẕ", "ẓ", "ᵶ", "ȥ", "ⱬ", "ᶎ", "ʒ", "ᶾ", "ǯ", "ʓ", "ƹ", "ƺ", "ᶚ", "θ", "ᶿ", "ɀ", "ꝣ"]
    "/zmh": ["⒜", "⒝", "⒞", "⒟", "⒠", "⒡", "⒢", "⒣", "⒤", "⒥", "⒦", "⒧", "⒨", "⒩", "⒪", "⒫", "⒬", "⒭", "⒮", "⒯", "⒰", "⒱", "⒲", "⒳", "⒴", "⒵"]
    "/zmq": ["ⓐ", "Ⓐ", "ⓑ", "Ⓑ", "ⓒ", "Ⓒ", "ⓓ", "Ⓓ", "ⓔ", "Ⓔ", "ⓕ", "Ⓕ", "ⓖ", "Ⓖ", "ⓗ", "Ⓗ", "ⓘ", "Ⓘ", "ⓙ", "Ⓙ", "ⓚ", "Ⓚ", "ⓛ", "Ⓛ", "ⓜ", "Ⓜ", "ⓝ", "Ⓝ", "ⓞ", "Ⓞ", "ⓟ", "Ⓟ", "ⓠ", "Ⓠ", "ⓡ", "Ⓡ", "ⓢ", "Ⓢ", "ⓣ", "Ⓣ", "ⓤ", "Ⓤ", "ⓥ", "Ⓥ", "ⓦ", "Ⓦ", "ⓧ", "Ⓧ", "ⓨ", "Ⓨ", "ⓩ", "Ⓩ"]
    "/zy": ["ㄅ", "ㄆ", "ㄇ", "ㄈ", "ㄉ", "ㄊ", "ㄋ", "ㄌ", "ㄍ", "ㄎ", "ㄏ", "ㄐ", "ㄑ", "ㄒ", "ㄓ", "ㄔ", "ㄕ", "ㄖ", "ㄗ", "ㄘ", "ㄙ", "ㄧ", "ㄨ", "ㄩ", "ㄚ", "ㄛ", "ㄜ", "ㄝ", "ㄞ", "ㄟ", "ㄠ", "ㄡ", "ㄢ", "ㄣ", "ㄤ", "ㄥ", "ㄦ", "ㄪ", "ㄫ", "ㄬ", "ㄭ", "ㆠ", "ㆡ", "ㆢ", "ㆣ", "ㆤ", "ㆥ", "ㆦ", "ㆧ", "ㆨ", "ㆩ", "ㆪ", "ㆫ", "ㆬ", "ㆭ", "ㆮ", "ㆯ", "ㆰ", "ㆱ", "ㆲ", "ㆳ", "ㆴ", "ㆵ", "ㆶ", "ㆷ"]
quick_symbol_text:
  0: "⓪"
  1: "①"
  2: "②"
  3: "③"
  4: "④"
  5: "⑤"
  6: "⑥"
  7: "⑦"
  8: "⑧"
  9: "⑨"
  a: "！"
  b: "%"
  c: "！”"
  d: "、"
  e: "（"
  f: "“"
  g: "”"
  h: "‘"
  i: "』"
  j: "’"
  k: "【"
  l: "】"
  m: "》"
  n: "《"
  o: "〖"
  p: "〗"
  q: "‰"
  r: "）"
  s: "……"
  t: "~"
  u: "『"
  v: "——"
  w: "？"
  x: "？”"
  y: "·"
  z: "。”"
radical_lookup:
  dictionary: wanxiang_radical
  enable_user_dict: false
  extra_tags:
    - reverse_stroke
  prefix: "`"
  tag: radical_lookup
  tips: "〔反查：部件|笔画〕"
radical_reverse_lookup:
  dictionary: wanxiang
  overwrite_comment: true
  tags:
    - radical_lookup
    - reverse_stroke
recognizer:
  import_preset: default
  patterns:
    calculator: "^V.*$"
    colon: "^[A-Za-z]+:.*"
    email: "^[A-Za-z][-_.0-9A-Za-z]*@.*$"
    lunar: "^N[0-9]{1,8}"
    number: "^R[0-9]+[.]?[0-9]*"
    punct: "^/([0-9]|10|[A-Za-z]+)$"
    quick_symbol: "^;.*$"
    radical_lookup: "^`[A-Za-z]*$"
    underscore: "^[A-Za-z]+_.*"
    unicode: "^U[a-f0-9]+"
    url: "^(www[.]|https?:|ftp[.:]|mailto:|file:).*$|^[a-z]+[.].+$"
    url_2: "^[A-Za-z]+[.].*"
reverse_stroke:
  db_class: tabledb
  dictionary: wanxiang_stroke
  enable_completion: true
  enable_user_dict: false
  preedit_format:
    - "xlit/hspnz/一丨丿丶乙/"
  suffix: "'"
  tag: reverse_stroke
s2hk:
  opencc_config: s2hk.json
  option_name: s2hk
  tags:
    - abc
s2t:
  opencc_config: s2t.json
  option_name: s2t
  tags:
    - abc
  tips: none
s2tw:
  opencc_config: s2tw.json
  option_name: s2tw
  tags:
    - abc
schema:
  author:
    - amzxyz
  dependencies:
    - wanxiang_en
    - wanxiang_radical
    - wanxiang_stroke
  description: |
    万象拼音标准版本，带声调的词库，支持语法模型，全拼、简拼、整句、声调辅助筛选，拥有超越大厂的输入体验！

  name: "万象拼音"
  schema_id: wanxiang
  version: "2024-11-11"
set_chord_composer:
  chord_composer:
    algebra: ["xlit|;,./|ACXZ|", "xform/([qwertasdfgzxcvb]+)/<$1>/", "xform/([yuiophjklAnmCXZ]+)/<$1>/", "xform=(<q>|<p>)=q=", "xform=(<w>|<o>)=w=", "xform=(<e>|<i>)=e=", "xform=(<r>|<u>)=r=", "xform=(<t>|<y>)=t=", "xform=(<ef>|<ji>)=y=", "xform=(<er>|<ui>)=u=", "xform=(<we>|<io>)=i=", "xform=(<wr>|<uo>)=o=", "xform=(<qr>|<up>)=p=", "xform=(<a>|<A>)=a=", "xform=(<s>|<l>)=s=", "xform=(<d>|<k>)=d=", "xform=(<f>|<j>)=f=", "xform=(<g>|<h>)=g=", "xform=(<se>|<il>)=h=", "xform=(<wf>|<jo>)=h=", "xform=(<df>|<jk>)=j=", "xform=(<sd>|<kl>)=k=", "xform=(<sf>|<jl>)=l=", "xform=(<z>|<Z>)=z=", "xform=(<x>|<X>)=x=", "xform=(<c>|<C>)=c=", "xform=(<v>|<m>)=v=", "xform=(<b>|<n>)=b=", "xform=(<af>|<jA>)=n=", "xform=(<cv>|<mC>)=m=", "xform=(<dg>)=,=", "xform=(<ag>)=.=", "xform=(<hk>)=!=", "xform=(<hA>)=?=", "xform=(<xc>|<CX>)=,=", "xform=(<xv>|<mX>)=.=", "xform=(<zx>|<XZ>)=!=", "xform=(<zv>|<mZ>)=?=", "xform=(<ad>|<kA>)=;=", "xform=(<as>|<lA>)=/=", "xform=(<vb>|<nm>)=/=", "xform=(<rt>|<yu>)=“=", "xform=(<et>|<yi>)=”=", "xform=(<qa>|<pA>)=~”=", "xform=(<aw>|<oA>)=^”=", "xform=(<ed>|<ik>)=!”=", "xform=(<rf>|<uj>)=?”=", "xform=(<ar>|<uA>)=:“=", "xform=(<sr>|<ul>)=.”=", "xform=(<qw>|<op>)=,“=", "xform=(<zf>|<jZ>)=+=", "xform=(<xf>|<jX>)=-=", "xform=(<cf>|<jC>)=%=", "xform=(<dr>|<uk>)=*=", "xform=(<qe>|<ip>)=@=", "xform=(<tg>)=:=", "xform=(<yh>)=#=", "xform=(<fg>)=~=", "xform=(<hj>)=^=", "xform/<1>/ /", "xform/<\"1>/\"/", "xform/^.*<.+>.*$//", "xform=，=\"\"\"\",=", "xform=。=\"\"\"\".=", "xform=！=\"\"\"\"!=", "xform=？=\"\"\"\"?="]
    alphabet: "qazwsxedcrfvtgbyhnujmik,ol.p;/ `"
    finish_chord_on_first_key_release: true
set_cn_en:
  user_dict: "en_dicts/flypy"
set_gongjian_mohu:
  __append:
    []
set_shuru_schema:
  __append:
    - "xform/^([a-z]+)$/$1④/"
    - "xform/^(.*)ā(.*)$/$1a$2①/"
    - "xform/^(.*)á(.*)$/$1a$2②/"
    - "xform/^(.*)ǎ(.*)$/$1a$2③/"
    - "xform/^(.*)à(.*)$/$1a$2④/"
    - "xform/^(.*)ō(.*)$/$1o$2①/"
    - "xform/^(.*)ó(.*)$/$1o$2②/"
    - "xform/^(.*)ǒ(.*)$/$1o$2③/"
    - "xform/^(.*)ò(.*)$/$1o$2④/"
    - "xform/^(.*)ē(.*)$/$1e$2①/"
    - "xform/^(.*)é(.*)$/$1e$2②/"
    - "xform/^(.*)ě(.*)$/$1e$2③/"
    - "xform/^(.*)è(.*)$/$1e$2④/"
    - "xform/^(.*)ī(.*)$/$1i$2①/"
    - "xform/^(.*)í(.*)$/$1i$2②/"
    - "xform/^(.*)ǐ(.*)$/$1i$2③/"
    - "xform/^(.*)ì(.*)$/$1i$2④/"
    - "xform/^(.*)ū(.*)$/$1u$2①/"
    - "xform/^(.*)ú(.*)$/$1u$2②/"
    - "xform/^(.*)ǔ(.*)$/$1u$2③/"
    - "xform/^(.*)ù(.*)$/$1u$2④/"
    - "xform/^(.*)ǖ(.*)$/$1v$2①/"
    - "xform/^(.*)ǘ(.*)$/$1v$2②/"
    - "xform/^(.*)ǚ(.*)$/$1v$2③/"
    - "xform/^(.*)ǜ(.*)$/$1v$2④/"
    - "xform/^(.*)ü(.*)$/$1v$2/"
    - "xform/^(.*)ń(.*)$/$1n$2②/"
    - "xform/^(.*)ň(.*)$/$1n$2③/"
    - "xform/^(.*)ǹ(.*)$/$1n$2④/"
    - "xlit/①②③④/7890/"
    - "derive/^ng(\\d)$/eng$1/"
    - "xform/^n(\\d)/en$1/"
    - "derive/^([jqxy])u(\\d)$/$1v$2/"
    - "derive/^([aoe])([ioun])(\\d)$/$1$1$2$3/"
    - "xform/^([aoe])(ng)?(\\d)$/$1$1$2$3/"
    - "xform/iu(\\d)$/Ⓠ$1/"
    - "xform/(.)ei(\\d)$/$1Ⓦ$2/"
    - "xform/uan(\\d)$/Ⓡ$1/"
    - "xform/[uv]e(\\d)$/Ⓣ$1/"
    - "xform/un(\\d)$/Ⓨ$1/"
    - "xform/^sh/Ⓤ/"
    - "xform/^ch/Ⓘ/"
    - "xform/^zh/Ⓥ/"
    - "xform/uo(\\d)$/Ⓞ$1/"
    - "xform/ie(\\d)$/Ⓟ$1/"
    - "xform/(.)i?ong(\\d)$/$1Ⓢ$2/"
    - "xform/ing|uai(\\d)$/Ⓚ$1/"
    - "xform/(.)ai(\\d)$/$1Ⓓ$2/"
    - "xform/(.)eng(\\d)$/$1Ⓖ$2/"
    - "xform/(.)en(\\d)$/$1Ⓕ$2/"
    - "xform/[iu]ang(\\d)$/Ⓛ$1/"
    - "xform/(.)ang(\\d)$/$1Ⓗ$2/"
    - "xform/ian(\\d)$/Ⓜ$1/"
    - "xform/(.)an(\\d)$/$1Ⓙ$2/"
    - "xform/(.)ou(\\d)$/$1Ⓩ$2/"
    - "xform/[iu]a(\\d)$/Ⓧ$1/"
    - "xform/iao(\\d)$/Ⓝ$1/"
    - "xform/(.)ao(\\d)$/$1Ⓒ$2/"
    - "xform/ui(\\d)$/Ⓥ$1/"
    - "xform/in(\\d)$/Ⓑ$1/"
    - "xlit/ⓆⓌⓇⓉⓎⓊⒾⓄⓅⓈⒹⒻⒼⒽⒿⓀⓁⓏⓍⒸⓋⒷⓃⓂ/qwrtyuiopsdfghjklzxcvbnm/"
    - "derive/^(..)(\\d)$/$1/"
    - "derive/^(.).+(\\d)$/$1$2/"
speller:
  algebra:
    - "xform/^([a-z]+)$/$1④/"
    - "xform/^(.*)ā(.*)$/$1a$2①/"
    - "xform/^(.*)á(.*)$/$1a$2②/"
    - "xform/^(.*)ǎ(.*)$/$1a$2③/"
    - "xform/^(.*)à(.*)$/$1a$2④/"
    - "xform/^(.*)ō(.*)$/$1o$2①/"
    - "xform/^(.*)ó(.*)$/$1o$2②/"
    - "xform/^(.*)ǒ(.*)$/$1o$2③/"
    - "xform/^(.*)ò(.*)$/$1o$2④/"
    - "xform/^(.*)ē(.*)$/$1e$2①/"
    - "xform/^(.*)é(.*)$/$1e$2②/"
    - "xform/^(.*)ě(.*)$/$1e$2③/"
    - "xform/^(.*)è(.*)$/$1e$2④/"
    - "xform/^(.*)ī(.*)$/$1i$2①/"
    - "xform/^(.*)í(.*)$/$1i$2②/"
    - "xform/^(.*)ǐ(.*)$/$1i$2③/"
    - "xform/^(.*)ì(.*)$/$1i$2④/"
    - "xform/^(.*)ū(.*)$/$1u$2①/"
    - "xform/^(.*)ú(.*)$/$1u$2②/"
    - "xform/^(.*)ǔ(.*)$/$1u$2③/"
    - "xform/^(.*)ù(.*)$/$1u$2④/"
    - "xform/^(.*)ǖ(.*)$/$1v$2①/"
    - "xform/^(.*)ǘ(.*)$/$1v$2②/"
    - "xform/^(.*)ǚ(.*)$/$1v$2③/"
    - "xform/^(.*)ǜ(.*)$/$1v$2④/"
    - "xform/^(.*)ü(.*)$/$1v$2/"
    - "xform/^(.*)ń(.*)$/$1n$2②/"
    - "xform/^(.*)ň(.*)$/$1n$2③/"
    - "xform/^(.*)ǹ(.*)$/$1n$2④/"
    - "xlit/①②③④/7890/"
    - "derive/^ng(\\d)$/eng$1/"
    - "xform/^n(\\d)/en$1/"
    - "derive/^([jqxy])u(\\d)$/$1v$2/"
    - "derive/^([aoe])([ioun])(\\d)$/$1$1$2$3/"
    - "xform/^([aoe])(ng)?(\\d)$/$1$1$2$3/"
    - "xform/iu(\\d)$/Ⓠ$1/"
    - "xform/(.)ei(\\d)$/$1Ⓦ$2/"
    - "xform/uan(\\d)$/Ⓡ$1/"
    - "xform/[uv]e(\\d)$/Ⓣ$1/"
    - "xform/un(\\d)$/Ⓨ$1/"
    - "xform/^sh/Ⓤ/"
    - "xform/^ch/Ⓘ/"
    - "xform/^zh/Ⓥ/"
    - "xform/uo(\\d)$/Ⓞ$1/"
    - "xform/ie(\\d)$/Ⓟ$1/"
    - "xform/(.)i?ong(\\d)$/$1Ⓢ$2/"
    - "xform/ing|uai(\\d)$/Ⓚ$1/"
    - "xform/(.)ai(\\d)$/$1Ⓓ$2/"
    - "xform/(.)eng(\\d)$/$1Ⓖ$2/"
    - "xform/(.)en(\\d)$/$1Ⓕ$2/"
    - "xform/[iu]ang(\\d)$/Ⓛ$1/"
    - "xform/(.)ang(\\d)$/$1Ⓗ$2/"
    - "xform/ian(\\d)$/Ⓜ$1/"
    - "xform/(.)an(\\d)$/$1Ⓙ$2/"
    - "xform/(.)ou(\\d)$/$1Ⓩ$2/"
    - "xform/[iu]a(\\d)$/Ⓧ$1/"
    - "xform/iao(\\d)$/Ⓝ$1/"
    - "xform/(.)ao(\\d)$/$1Ⓒ$2/"
    - "xform/ui(\\d)$/Ⓥ$1/"
    - "xform/in(\\d)$/Ⓑ$1/"
    - "xlit/ⓆⓌⓇⓉⓎⓊⒾⓄⓅⓈⒹⒻⒼⒽⒿⓀⓁⓏⓍⒸⓋⒷⓃⓂ/qwrtyuiopsdfghjklzxcvbnm/"
    - "derive/^(..)(\\d)$/$1/"
    - "derive/^(.).+(\\d)$/$1$2/"
  alphabet: "zyxwvutsrqponmlkjihgfedcbaZYXWVUTSRQPONMLKJIHGFEDCBA7890`;/"
  delimiter: "''"
  initials: zyxwvutsrqponmlkjihgfedcbaZYXWVUTSRQPONMLKJIHGFEDCBA
  tone_isolate: true
  visual_delimiter: " "
super_comment:
  candidate_length: 2
  corrector_type: "{comment}"
switches:
  - name: ascii_mode
    states: ["中文", "英文"]
  - name: ascii_punct
    states: ["中标", "英标"]
  - name: full_shape
    states: ["半角", "全角"]
  - name: emoji
    states: ["表情关", "表情开"]
  - name: chinese_english
    states: ["翻译关", "翻译开"]
  - name: tone_display
    states: ["声调关", "声调开"]
  - name: tone_hint
    states: ["读音关", "读音开"]
  - name: super_tips
    reset: 1
    states: ["提示关", "提示开"]
  - options: [s2s, s2t, s2hk, s2tw]
    reset: 3
    states: ["简体", "通繁", "港繁", "臺繁"]
  - name: prediction
    states: ["预测关", "预测开"]
  - name: search_single_char
    states: ["正常", "单字"]
translator:
  always_show_comments: true
  comment_format:
    {}
  contextual_suggestions: false
  dictionary: wanxiang
  disable_user_dict_for_patterns: "^[a-z]{1,6}"
  enable_completion: true
  enable_correction: false
  enable_encoder: true
  enable_sentence: false
  enable_user_dict: true
  encode_commit_history: true
  initial_quality: 3
  max_homographs: 5
  max_homophones: 5
  packs:
    - userxx
  preedit_format:
    - "xform/7/1/"
    - "xform/8/2/"
    - "xform/9/3/"
    - "xform/0/4/"
  spelling_hints: 30
user_dict_set:
  dictionary: wanxiang
  enable_completion: false
  enable_sentence: false
  enable_user_dict: false
  initial_quality: 0
  preedit_format:
    - "xform/7/1/"
    - "xform/8/2/"
    - "xform/9/3/"
    - "xform/0/4/"
  spelling_hints: 100
  user_dict: zc
wanxiang_en:
  comment_format:
    - "xform/.*//"
  dictionary: wanxiang_en
  enable_sentence: false
  enable_user_dict: false
  initial_quality: 1.1
"乱序17":
  __append:
    - "xlit/āáǎàōóǒòēéěèīíǐìūúǔùǖǘǚǜü/aaaaooooeeeeiiiiuuuuvvvvv/"
    - "xform/ń|ň|ǹ/en/"
    - "xform/\\bng\\b/eng/"
    - "xform/ńg|ňg|ǹg/eng/"
    - "erase/^xx$/"
    - "derive/^([jqxy])u$/$1Ⓥ/"
    - "xform/^ch/Ⓒ/"
    - "xform/^c/Ⓕ/"
    - "xform/^k/Ⓙ/"
    - "xform/^([aoe].*)$/Ⓠ$1/"
    - "xform/^p/Ⓗ/"
    - "xform/^r/Ⓝ/"
    - "xform/^sh/Ⓢ/"
    - "xform/^s/Ⓜ/"
    - "xform/^zh/Ⓩ/"
    - "xform/^z/Ⓦ/"
    - "xform/uang$/Ⓠ/"
    - "xform/iang$/Ⓒ/"
    - "xform/iong$/Ⓑ/"
    - "xform/ang$/Ⓩ/"
    - "xform/eng$/Ⓨ/"
    - "xform/ian$/Ⓠ/"
    - "xform/iao$/Ⓩ/"
    - "xform/ing$/Ⓨ/"
    - "xform/ong$/Ⓣ/"
    - "xform/uai$/Ⓧ/"
    - "xform/uan$/Ⓧ/"
    - "xform/ai$/Ⓛ/"
    - "xform/an$/Ⓝ/"
    - "xform/ao$/Ⓑ/"
    - "xform/ei$/Ⓖ/"
    - "xform/en$/Ⓢ/"
    - "xform/er$/Ⓣ/"
    - "xform/ua$/Ⓗ/"
    - "xform/ie$/Ⓜ/"
    - "xform/in$/Ⓢ/"
    - "xform/iu$/Ⓕ/"
    - "xform/ou$/Ⓕ/"
    - "xform/ia$/Ⓗ/"
    - "xform/[uv]e$/Ⓛ/"
    - "xform/ui$/Ⓒ/"
    - "xform/un$/Ⓖ/"
    - "xform/uo$/Ⓜ/"
    - "xform/a$/Ⓗ/"
    - "xform/e$/Ⓦ/"
    - "xform/i$/Ⓙ/"
    - "xform/o$/Ⓧ/"
    - "xform/u$/Ⓓ/"
    - "xform/v$/Ⓧ/"
    - "xlit/ⓆⓌⒺⓇⓉⓎⓄⓅⒶⓈⒹⒻⒼⒽⒿⓀⓁⓏⓍⒸⓋⒷⓃⓂ/qwertyopasdfghjklzxcvbnm/"
"全拼":
  __append:
    - "xform/^([a-z]+)$/$1④/"
    - "xform/^(.*)ā(.*)$/$1a$2①/"
    - "xform/^(.*)á(.*)$/$1a$2②/"
    - "xform/^(.*)ǎ(.*)$/$1a$2③/"
    - "xform/^(.*)à(.*)$/$1a$2④/"
    - "xform/^(.*)ō(.*)$/$1o$2①/"
    - "xform/^(.*)ó(.*)$/$1o$2②/"
    - "xform/^(.*)ǒ(.*)$/$1o$2③/"
    - "xform/^(.*)ò(.*)$/$1o$2④/"
    - "xform/^(.*)ē(.*)$/$1e$2①/"
    - "xform/^(.*)é(.*)$/$1e$2②/"
    - "xform/^(.*)ě(.*)$/$1e$2③/"
    - "xform/^(.*)è(.*)$/$1e$2④/"
    - "xform/^(.*)ī(.*)$/$1i$2①/"
    - "xform/^(.*)í(.*)$/$1i$2②/"
    - "xform/^(.*)ǐ(.*)$/$1i$2③/"
    - "xform/^(.*)ì(.*)$/$1i$2④/"
    - "xform/^(.*)ū(.*)$/$1u$2①/"
    - "xform/^(.*)ú(.*)$/$1u$2②/"
    - "xform/^(.*)ǔ(.*)$/$1u$2③/"
    - "xform/^(.*)ù(.*)$/$1u$2④/"
    - "xform/^(.*)ǖ(.*)$/$1v$2①/"
    - "xform/^(.*)ǘ(.*)$/$1v$2②/"
    - "xform/^(.*)ǚ(.*)$/$1v$2③/"
    - "xform/^(.*)ǜ(.*)$/$1v$2④/"
    - "xform/^(.*)ü(.*)$/$1v$2/"
    - "xform/^(.*)ń(.*)$/$1n$2②/"
    - "xform/^(.*)ň(.*)$/$1n$2③/"
    - "xform/^(.*)ǹ(.*)$/$1n$2④/"
    - "xlit/①②③④/7890"
    - "derive/^ng(\\d)$/eng$1/"
    - "xform/^n(\\d)/en$1/"
    - "derive/([nl])ve(\\d)/$1ue$2/"
    - "derive/^([jqx])v(\\d)$/$1u$2/"
    - "abbrev/^([zcs]h).+$/$1/"
    - "abbrev/^([zcs]h).+(\\d)$/$1$2/"
    - "abbrev/^([qwrtypsdfghjklzxcbnm]).+$/$1/"
    - "abbrev/^([qwrtypsdfghjklzxcbnm]).+(\\d)$/$1$2/"
    - "abbrev/^([aoe])([ioun])(\\d)$/$1/"
    - "abbrev/^([aoe])([ioun])(\\d)$/$1$3/"
    - "derive/(.+)(\\d)/$1/"
"小鹤双拼":
  __append:
    - "xform/^([a-z]+)$/$1④/"
    - "xform/^(.*)ā(.*)$/$1a$2①/"
    - "xform/^(.*)á(.*)$/$1a$2②/"
    - "xform/^(.*)ǎ(.*)$/$1a$2③/"
    - "xform/^(.*)à(.*)$/$1a$2④/"
    - "xform/^(.*)ō(.*)$/$1o$2①/"
    - "xform/^(.*)ó(.*)$/$1o$2②/"
    - "xform/^(.*)ǒ(.*)$/$1o$2③/"
    - "xform/^(.*)ò(.*)$/$1o$2④/"
    - "xform/^(.*)ē(.*)$/$1e$2①/"
    - "xform/^(.*)é(.*)$/$1e$2②/"
    - "xform/^(.*)ě(.*)$/$1e$2③/"
    - "xform/^(.*)è(.*)$/$1e$2④/"
    - "xform/^(.*)ī(.*)$/$1i$2①/"
    - "xform/^(.*)í(.*)$/$1i$2②/"
    - "xform/^(.*)ǐ(.*)$/$1i$2③/"
    - "xform/^(.*)ì(.*)$/$1i$2④/"
    - "xform/^(.*)ū(.*)$/$1u$2①/"
    - "xform/^(.*)ú(.*)$/$1u$2②/"
    - "xform/^(.*)ǔ(.*)$/$1u$2③/"
    - "xform/^(.*)ù(.*)$/$1u$2④/"
    - "xform/^(.*)ǖ(.*)$/$1v$2①/"
    - "xform/^(.*)ǘ(.*)$/$1v$2②/"
    - "xform/^(.*)ǚ(.*)$/$1v$2③/"
    - "xform/^(.*)ǜ(.*)$/$1v$2④/"
    - "xform/^(.*)ü(.*)$/$1v$2/"
    - "xform/^(.*)ń(.*)$/$1n$2②/"
    - "xform/^(.*)ň(.*)$/$1n$2③/"
    - "xform/^(.*)ǹ(.*)$/$1n$2④/"
    - "xlit/①②③④/7890/"
    - "derive/^ng(\\d)$/eng$1/"
    - "xform/^n(\\d)/en$1/"
    - "derive/^([jqxy])u(\\d)$/$1v$2/"
    - "derive/^([aoe])([ioun])(\\d)$/$1$1$2$3/"
    - "xform/^([aoe])(ng)?(\\d)$/$1$1$2$3/"
    - "xform/iu(\\d)$/Ⓠ$1/"
    - "xform/(.)ei(\\d)$/$1Ⓦ$2/"
    - "xform/uan(\\d)$/Ⓡ$1/"
    - "xform/[uv]e(\\d)$/Ⓣ$1/"
    - "xform/un(\\d)$/Ⓨ$1/"
    - "xform/^sh/Ⓤ/"
    - "xform/^ch/Ⓘ/"
    - "xform/^zh/Ⓥ/"
    - "xform/uo(\\d)$/Ⓞ$1/"
    - "xform/ie(\\d)$/Ⓟ$1/"
    - "xform/(.)i?ong(\\d)$/$1Ⓢ$2/"
    - "xform/ing|uai(\\d)$/Ⓚ$1/"
    - "xform/(.)ai(\\d)$/$1Ⓓ$2/"
    - "xform/(.)eng(\\d)$/$1Ⓖ$2/"
    - "xform/(.)en(\\d)$/$1Ⓕ$2/"
    - "xform/[iu]ang(\\d)$/Ⓛ$1/"
    - "xform/(.)ang(\\d)$/$1Ⓗ$2/"
    - "xform/ian(\\d)$/Ⓜ$1/"
    - "xform/(.)an(\\d)$/$1Ⓙ$2/"
    - "xform/(.)ou(\\d)$/$1Ⓩ$2/"
    - "xform/[iu]a(\\d)$/Ⓧ$1/"
    - "xform/iao(\\d)$/Ⓝ$1/"
    - "xform/(.)ao(\\d)$/$1Ⓒ$2/"
    - "xform/ui(\\d)$/Ⓥ$1/"
    - "xform/in(\\d)$/Ⓑ$1/"
    - "xlit/ⓆⓌⓇⓉⓎⓊⒾⓄⓅⓈⒹⒻⒼⒽⒿⓀⓁⓏⓍⒸⓋⒷⓃⓂ/qwrtyuiopsdfghjklzxcvbnm/"
    - "derive/^(..)(\\d)$/$1/"
    - "derive/^(.).+(\\d)$/$1$2/"
"微软双拼":
  __append:
    - "xform/^([a-z]+)$/$1④/"
    - "xform/^(.*)ā(.*)$/$1a$2①/"
    - "xform/^(.*)á(.*)$/$1a$2②/"
    - "xform/^(.*)ǎ(.*)$/$1a$2③/"
    - "xform/^(.*)à(.*)$/$1a$2④/"
    - "xform/^(.*)ō(.*)$/$1o$2①/"
    - "xform/^(.*)ó(.*)$/$1o$2②/"
    - "xform/^(.*)ǒ(.*)$/$1o$2③/"
    - "xform/^(.*)ò(.*)$/$1o$2④/"
    - "xform/^(.*)ē(.*)$/$1e$2①/"
    - "xform/^(.*)é(.*)$/$1e$2②/"
    - "xform/^(.*)ě(.*)$/$1e$2③/"
    - "xform/^(.*)è(.*)$/$1e$2④/"
    - "xform/^(.*)ī(.*)$/$1i$2①/"
    - "xform/^(.*)í(.*)$/$1i$2②/"
    - "xform/^(.*)ǐ(.*)$/$1i$2③/"
    - "xform/^(.*)ì(.*)$/$1i$2④/"
    - "xform/^(.*)ū(.*)$/$1u$2①/"
    - "xform/^(.*)ú(.*)$/$1u$2②/"
    - "xform/^(.*)ǔ(.*)$/$1u$2③/"
    - "xform/^(.*)ù(.*)$/$1u$2④/"
    - "xform/^(.*)ǖ(.*)$/$1v$2①/"
    - "xform/^(.*)ǘ(.*)$/$1v$2②/"
    - "xform/^(.*)ǚ(.*)$/$1v$2③/"
    - "xform/^(.*)ǜ(.*)$/$1v$2④/"
    - "xform/^(.*)ü(.*)$/$1v$2/"
    - "xform/^(.*)ń(.*)$/$1n$2②/"
    - "xform/^(.*)ň(.*)$/$1n$2③/"
    - "xform/^(.*)ǹ(.*)$/$1n$2④/"
    - "xlit/①②③④/7890/"
    - "derive/^ng(\\d)$/eng$1/"
    - "xform/^n(\\d)/en$1/"
    - "derive/^([jqxy])u(\\d)$/$1v$2/"
    - "derive/^([aoe].*)(\\d)$/o$1$2/"
    - "xform/^([ae])(.*)(\\d)$/$1$1$2$3/"
    - "xform/iu(\\d)$/Ⓠ$1/"
    - "xform/[iu]a(\\d)$/Ⓦ$1/"
    - "xform/er|[uv]an(\\d)$/Ⓡ$1/"
    - "xform/[uv]e(\\d)$/Ⓣ$1/"
    - "xform/v|uai(\\d)$/Ⓨ$1/"
    - "xform/^sh/Ⓤ/"
    - "xform/^ch/Ⓘ/"
    - "xform/^zh/Ⓥ/"
    - "xform/uo(\\d)$/Ⓞ$1/"
    - "xform/[uv]n(\\d)$/Ⓟ$1/"
    - "xform/(.)i?ong(\\d)$/$1Ⓢ$2/"
    - "xform/[iu]ang(\\d)$/Ⓓ$1/"
    - "xform/(.)en(\\d)$/$1Ⓕ$2/"
    - "xform/(.)eng(\\d)$/$1Ⓖ$2/"
    - "xform/(.)ang(\\d)$/$1Ⓗ$2/"
    - "xform/ian(\\d)$/Ⓜ$1/"
    - "xform/(.)an(\\d)$/$1Ⓙ$2/"
    - "xform/iao(\\d)$/Ⓒ$1/"
    - "xform/(.)ao(\\d)$/$1Ⓚ$2/"
    - "xform/(.)ai(\\d)$/$1Ⓛ$2/"
    - "xform/(.)ei(\\d)$/$1Ⓩ$2/"
    - "xform/ie(\\d)$/Ⓧ$1/"
    - "xform/ui(\\d)$/Ⓥ$1/"
    - "derive/Ⓣ(\\d)$/Ⓥ$1/"
    - "xform/(.)ou(\\d)$/$1Ⓑ$2/"
    - "xform/in(\\d)$/Ⓝ$1/"
    - "xform/ing(\\d)$/;$1/"
    - "xlit/ⓆⓌⓇⓉⓎⓊⒾⓄⓅⓈⒹⒻⒼⒽⓂⒿⒸⓀⓁⓏⓍⓋⒷⓃ/qwrtyuiopsdfghmjcklzxvbn/"
    - "derive/^(..)(\\d)$/$1/"
    - "derive/^(.).+(\\d)$/$1$2/"
"拼音加加":
  __append:
    - "xform/^([a-z]+)$/$1④/"
    - "xform/^(.*)ā(.*)$/$1a$2①/"
    - "xform/^(.*)á(.*)$/$1a$2②/"
    - "xform/^(.*)ǎ(.*)$/$1a$2③/"
    - "xform/^(.*)à(.*)$/$1a$2④/"
    - "xform/^(.*)ō(.*)$/$1o$2①/"
    - "xform/^(.*)ó(.*)$/$1o$2②/"
    - "xform/^(.*)ǒ(.*)$/$1o$2③/"
    - "xform/^(.*)ò(.*)$/$1o$2④/"
    - "xform/^(.*)ē(.*)$/$1e$2①/"
    - "xform/^(.*)é(.*)$/$1e$2②/"
    - "xform/^(.*)ě(.*)$/$1e$2③/"
    - "xform/^(.*)è(.*)$/$1e$2④/"
    - "xform/^(.*)ī(.*)$/$1i$2①/"
    - "xform/^(.*)í(.*)$/$1i$2②/"
    - "xform/^(.*)ǐ(.*)$/$1i$2③/"
    - "xform/^(.*)ì(.*)$/$1i$2④/"
    - "xform/^(.*)ū(.*)$/$1u$2①/"
    - "xform/^(.*)ú(.*)$/$1u$2②/"
    - "xform/^(.*)ǔ(.*)$/$1u$2③/"
    - "xform/^(.*)ù(.*)$/$1u$2④/"
    - "xform/^(.*)ǖ(.*)$/$1v$2①/"
    - "xform/^(.*)ǘ(.*)$/$1v$2②/"
    - "xform/^(.*)ǚ(.*)$/$1v$2③/"
    - "xform/^(.*)ǜ(.*)$/$1v$2④/"
    - "xform/^(.*)ü(.*)$/$1v$2/"
    - "xform/^(.*)ń(.*)$/$1n$2②/"
    - "xform/^(.*)ň(.*)$/$1n$2③/"
    - "xform/^(.*)ǹ(.*)$/$1n$2④/"
    - "xlit/①②③④/7890/"
    - "derive/^ng(\\d)$/eng$1/"
    - "xform/^n(\\d)/en$1/"
    - "derive/^([jqxy])u$/$1v/"
    - "derive/^([aoe].*)$/o$1/"
    - "xform/^([ae])(.*)$/$1$1$2/"
    - "xform/iu$/Ⓝ/"
    - "xform/[iu]a$/Ⓑ/"
    - "xform/er$|ing$/Ⓠ/"
    - "xform/[uv]an$/Ⓒ/"
    - "xform/[uv]e$|uai$/Ⓧ/"
    - "xform/^sh/Ⓘ/"
    - "xform/^ch/Ⓤ/"
    - "xform/^zh/Ⓥ/"
    - "xform/uo$/Ⓞ/"
    - "xform/[uv]n$/Ⓩ/"
    - "xform/i?ong$/Ⓨ/"
    - "xform/[iu]ang$/Ⓗ/"
    - "xform/(.)en$/$1Ⓡ/"
    - "xform/(.)eng$/$1Ⓣ/"
    - "xform/(.)ang$/$1Ⓖ/"
    - "xform/ian$/Ⓙ/"
    - "xform/(.)an$/$1Ⓕ/"
    - "xform/iao$/Ⓚ/"
    - "xform/(.)ao$/$1Ⓓ/"
    - "xform/(.)ai$/$1Ⓢ/"
    - "xform/(.)ei$/$1Ⓦ/"
    - "xform/ie$/Ⓜ/"
    - "xform/ui$/Ⓥ/"
    - "xform/(.)ou$/$1Ⓟ/"
    - "xform/in$/Ⓛ/"
    - "xlit/ⓆⓌⓇⓉⓎⓊⒾⓄⓅⓈⒹⒻⒼⒽⓂⒿⒸⓀⓁⓏⓍⓋⒷⓃ/qwrtyuiopsdfghmjcklzxvbn/"
"搜狗双拼":
  __append:
    - "xform/^([a-z]+)$/$1④/"
    - "xform/^(.*)ā(.*)$/$1a$2①/"
    - "xform/^(.*)á(.*)$/$1a$2②/"
    - "xform/^(.*)ǎ(.*)$/$1a$2③/"
    - "xform/^(.*)à(.*)$/$1a$2④/"
    - "xform/^(.*)ō(.*)$/$1o$2①/"
    - "xform/^(.*)ó(.*)$/$1o$2②/"
    - "xform/^(.*)ǒ(.*)$/$1o$2③/"
    - "xform/^(.*)ò(.*)$/$1o$2④/"
    - "xform/^(.*)ē(.*)$/$1e$2①/"
    - "xform/^(.*)é(.*)$/$1e$2②/"
    - "xform/^(.*)ě(.*)$/$1e$2③/"
    - "xform/^(.*)è(.*)$/$1e$2④/"
    - "xform/^(.*)ī(.*)$/$1i$2①/"
    - "xform/^(.*)í(.*)$/$1i$2②/"
    - "xform/^(.*)ǐ(.*)$/$1i$2③/"
    - "xform/^(.*)ì(.*)$/$1i$2④/"
    - "xform/^(.*)ū(.*)$/$1u$2①/"
    - "xform/^(.*)ú(.*)$/$1u$2②/"
    - "xform/^(.*)ǔ(.*)$/$1u$2③/"
    - "xform/^(.*)ù(.*)$/$1u$2④/"
    - "xform/^(.*)ǖ(.*)$/$1v$2①/"
    - "xform/^(.*)ǘ(.*)$/$1v$2②/"
    - "xform/^(.*)ǚ(.*)$/$1v$2③/"
    - "xform/^(.*)ǜ(.*)$/$1v$2④/"
    - "xform/^(.*)ü(.*)$/$1v$2/"
    - "xform/^(.*)ń(.*)$/$1n$2②/"
    - "xform/^(.*)ň(.*)$/$1n$2③/"
    - "xform/^(.*)ǹ(.*)$/$1n$2④/"
    - "xlit/①②③④/7890/"
    - "derive/^ng(\\d)$/eng$1/"
    - "xform/^n(\\d)/en$1/"
    - "derive/^([jqxy])u(\\d)$/$1v$2/"
    - "derive/^([aoe].*)(\\d)$/o$1$2/"
    - "xform/^([ae])(.*)(\\d)$/$1$1$2$3/"
    - "xform/iu(\\d)$/Ⓠ$1/"
    - "xform/[iu]a(\\d)$/Ⓦ$1/"
    - "xform/er|[uv]an(\\d)$/Ⓡ$1/"
    - "xform/[uv]e(\\d)$/Ⓣ$1/"
    - "xform/v|uai(\\d)$/Ⓨ$1/"
    - "xform/^sh/Ⓤ/"
    - "xform/^ch/Ⓘ/"
    - "xform/^zh/Ⓥ/"
    - "xform/uo(\\d)$/Ⓞ$1/"
    - "xform/[uv]n(\\d)$/Ⓟ$1/"
    - "xform/(.)i?ong(\\d)$/$1Ⓢ$2/"
    - "xform/[iu]ang(\\d)$/Ⓓ$1/"
    - "xform/(.)en(\\d)$/$1Ⓕ$2/"
    - "xform/(.)eng(\\d)$/$1Ⓖ$2/"
    - "xform/(.)ang(\\d)$/$1Ⓗ$2/"
    - "xform/ian(\\d)$/Ⓜ$1/"
    - "xform/(.)an(\\d)$/$1Ⓙ$2/"
    - "xform/iao(\\d)$/Ⓒ$1/"
    - "xform/(.)ao(\\d)$/$1Ⓚ$2/"
    - "xform/(.)ai(\\d)$/$1Ⓛ$2/"
    - "xform/(.)ei(\\d)$/$1Ⓩ$2/"
    - "xform/ie(\\d)$/Ⓧ$1/"
    - "xform/ui(\\d)$/Ⓥ$1/"
    - "xform/(.)ou(\\d)$/$1Ⓑ$2/"
    - "xform/in(\\d)$/Ⓝ$1/"
    - "xform/ing(\\d)$/;$1/"
    - "xlit/ⓆⓌⓇⓉⓎⓊⒾⓄⓅⓈⒹⒻⒼⒽⓂⒿⒸⓀⓁⓏⓍⓋⒷⓃ/qwrtyuiopsdfghmjcklzxvbn/"
    - "derive/^(..)(\\d)$/$1/"
    - "derive/^(.).+(\\d)$/$1$2/"
"智能ABC":
  __append:
    - "xform/^([a-z]+)$/$1④/"
    - "xform/^(.*)ā(.*)$/$1a$2①/"
    - "xform/^(.*)á(.*)$/$1a$2②/"
    - "xform/^(.*)ǎ(.*)$/$1a$2③/"
    - "xform/^(.*)à(.*)$/$1a$2④/"
    - "xform/^(.*)ō(.*)$/$1o$2①/"
    - "xform/^(.*)ó(.*)$/$1o$2②/"
    - "xform/^(.*)ǒ(.*)$/$1o$2③/"
    - "xform/^(.*)ò(.*)$/$1o$2④/"
    - "xform/^(.*)ē(.*)$/$1e$2①/"
    - "xform/^(.*)é(.*)$/$1e$2②/"
    - "xform/^(.*)ě(.*)$/$1e$2③/"
    - "xform/^(.*)è(.*)$/$1e$2④/"
    - "xform/^(.*)ī(.*)$/$1i$2①/"
    - "xform/^(.*)í(.*)$/$1i$2②/"
    - "xform/^(.*)ǐ(.*)$/$1i$2③/"
    - "xform/^(.*)ì(.*)$/$1i$2④/"
    - "xform/^(.*)ū(.*)$/$1u$2①/"
    - "xform/^(.*)ú(.*)$/$1u$2②/"
    - "xform/^(.*)ǔ(.*)$/$1u$2③/"
    - "xform/^(.*)ù(.*)$/$1u$2④/"
    - "xform/^(.*)ǖ(.*)$/$1v$2①/"
    - "xform/^(.*)ǘ(.*)$/$1v$2②/"
    - "xform/^(.*)ǚ(.*)$/$1v$2③/"
    - "xform/^(.*)ǜ(.*)$/$1v$2④/"
    - "xform/^(.*)ü(.*)$/$1v$2/"
    - "xform/^(.*)ń(.*)$/$1n$2②/"
    - "xform/^(.*)ň(.*)$/$1n$2③/"
    - "xform/^(.*)ǹ(.*)$/$1n$2④/"
    - "xlit/①②③④/7890/"
    - "derive/^ng(\\d)$/eng$1/"
    - "xform/^n(\\d)/en$1/"
    - "xform/^zh/Ⓐ/"
    - "xform/^ch/Ⓔ/"
    - "xform/^sh/Ⓥ/"
    - "xform/^([aoe].*)(\\d)$/Ⓞ$1$2/"
    - "xform/ei(\\d)$/Ⓠ$1/"
    - "xform/ian(\\d)$/Ⓦ$1/"
    - "xform/er|iu(\\d)$/Ⓡ$1/"
    - "xform/[iu]ang(\\d)$/Ⓣ$1/"
    - "xform/ing(\\d)$/Ⓨ$1/"
    - "xform/uo(\\d)$/Ⓞ$1/"
    - "xform/uan(\\d)$/Ⓟ$1/"
    - "xform/(.)i?ong(\\d)$/$1Ⓢ$2/"
    - "xform/[iu]a(\\d)$/Ⓓ$1/"
    - "xform/en(\\d)$/Ⓕ$1/"
    - "xform/eng(\\d)$/Ⓖ$1/"
    - "xform/ang(\\d)$/Ⓗ$1/"
    - "xform/an(\\d)$/Ⓙ$1/"
    - "xform/iao(\\d)$/Ⓩ$1/"
    - "xform/ao(\\d)$/Ⓚ$1/"
    - "xform/in|uai(\\d)$/Ⓒ$1/"
    - "xform/ai(\\d)$/Ⓛ$1/"
    - "xform/ie(\\d)$/Ⓧ$1/"
    - "xform/ou(\\d)$/Ⓑ$1/"
    - "xform/un(\\d)$/Ⓝ$1/"
    - "xform/[uv]e|ui(\\d)$/Ⓜ$1/"
    - "xlit/ⓆⓌⒺⓇⓉⓎⓄⓅⒶⓈⒹⒻⒼⒽⒿⓀⓁⓏⓍⒸⓋⒷⓃⓂ/qwertyopasdfghjklzxcvbnm/"
    - "derive/^(..)(\\d)$/$1/"
    - "derive/^(.).+(\\d)$/$1$2/"
"汉心龙":
  __append:
    - "xform/^(a|ā|á|ǎ|à)/Q$1/"
    - "xform/^(o|ō|ó|ǒ|ò)/R$1/"
    - "xform/^(e|ē|é|ě|è)/B$1/"
    - "xform/^([jqxy])u/$1ü/"
    - "xform/^([jqxy])ū/$1ǖ/"
    - "xform/^([jqxy])ú/$1ǘ/"
    - "xform/^([jqxy])ǔ/$1ǚ/"
    - "xform/^([jqxy])ù/$1ǜ/"
    - "xform/^ǹg$/Bèng/"
    - "xform/^ňg$/Běng/"
    - "xform/^ńg$/Béng/"
    - "xform/^ng$/beng/"
    - "xform/^ǹ$/Bèn/"
    - "xform/^ň$/Běn/"
    - "xform/^ń$/Bén/"
    - "xform/^n$/Ben/"
    - "xform/^sh/T/"
    - "xform/^ch/S/"
    - "xform/^zh/K/"
    - "xform/^a/Q/"
    - "xform/^p/W/"
    - "xform/^j/E/"
    - "xform/^o/R/"
    - "xform/^n/Y/"
    - "xform/^k/U/"
    - "xform/^b/I/"
    - "xform/^t/O/"
    - "xform/^m/P/"
    - "xform/^q/A/"
    - "xform/^l/D/"
    - "xform/^r/F/"
    - "xform/^w/G/"
    - "xform/^d/H/"
    - "xform/^y/J/"
    - "xform/^g/L/"
    - "xform/^s/Z/"
    - "xform/^c/X/"
    - "xform/^x/C/"
    - "xform/^f/V/"
    - "xform/^e/B/"
    - "xform/^z/N/"
    - "xform/^h/M/"
    - "xform/^(.)(ēn|en|iǎn|ǒng|iáng|uá|ó|án|ǐng)$/$1Q/"
    - "xform/^(.)(ào|èn|èng|iě|iàng)$/$1W/"
    - "xform/^(.)(ü|ǖ|iù|ā|a|uì|uǎng)$/$1E/"
    - "xform/^(.)(ìn|è|à|ě|ǎi|áo)$/$1R/"
    - "xform/^(.)(ō|o|ìòng|ǎo|óu)$/$1T/"
    - "xform/^(.)(én|ià|ang|āng|iú|ǐn|üē|uē|üe|ue)$/$1Y/"
    - "xform/^(.)(àng|iàn|ao|āo)$/$1U/"
    - "xform/^(.)(ù|uán|ué|üé)$/$1I/"
    - "xform/^(.)(éng|iā|ia|ān|an|èr|uè|üè|iu|iū|uài|iáo)$/$1O/"
    - "xform/^(.)(uǒ|iè|iào|ěr|ǎng)$/$1P/"
    - "xform/^(.)(āi|ai|óng|ìng|uāng|uang|á|ēr|er|iǎ)$/$1A/"
    - "xform/^(.)(uō|uo|ǚ|uó|àn|ín|ūn|un|iōng|iong)$/$1S/"
    - "xform/^(.)(ǒu|èi|é|iāo|iao|iá)$/$1D/"
    - "xform/^(.)(ǐ|uān|uan|uà|ò|uǎ)$/$1F/"
    - "xform/^(.)(ī|i|ěn|ěi|uáng)$/$1G/"
    - "xform/^(.)(ǜ|ēng|eng|iē|ie|éi|ún|uāi|uai)$/$1H/"
    - "xform/^(.)(ái|ǒ|ū|u|ér|īng|ing|uǎi)$/$1J/"
    - "xform/^(.)(ē|e|íng|òu|ié)$/$1K/"
    - "xform/^(.)(ì|uī|ui)$/$1L/"
    - "xform/^(.)(uā|ua|òng|uǐ|áng|iǔ|iǎo)$/$1Z/"
    - "xform/^(.)(uò|ōu|ou|iān|ian|ēi|ei|ùn)$/$1X/"
    - "xform/^(.)(ǔ|uàn)$/$1C/"
    - "xform/^(.)(ǎ|uái|ōng|ong|īn|in|ióng|iǒng)$/$1V/"
    - "xform/^(.)(ài|ián|üě|uě|uí|uǎn|uàng|ǘ)$/$1B/"
    - "xform/^(.)(í|iǎng|ǎn)$/$1N/"
    - "xform/^(.)(ú|iāng|iang|ěng|ǔn)$/$1M/"
    - "xlit/QWERTYUIOPASDFGHMJCKLZXVBN/qwertyuiopasdfghmjcklzxvbn/"
"紫光双拼":
  __append:
    - "xform/^([a-z]+)$/$1④/"
    - "xform/^(.*)ā(.*)$/$1a$2①/"
    - "xform/^(.*)á(.*)$/$1a$2②/"
    - "xform/^(.*)ǎ(.*)$/$1a$2③/"
    - "xform/^(.*)à(.*)$/$1a$2④/"
    - "xform/^(.*)ō(.*)$/$1o$2①/"
    - "xform/^(.*)ó(.*)$/$1o$2②/"
    - "xform/^(.*)ǒ(.*)$/$1o$2③/"
    - "xform/^(.*)ò(.*)$/$1o$2④/"
    - "xform/^(.*)ē(.*)$/$1e$2①/"
    - "xform/^(.*)é(.*)$/$1e$2②/"
    - "xform/^(.*)ě(.*)$/$1e$2③/"
    - "xform/^(.*)è(.*)$/$1e$2④/"
    - "xform/^(.*)ī(.*)$/$1i$2①/"
    - "xform/^(.*)í(.*)$/$1i$2②/"
    - "xform/^(.*)ǐ(.*)$/$1i$2③/"
    - "xform/^(.*)ì(.*)$/$1i$2④/"
    - "xform/^(.*)ū(.*)$/$1u$2①/"
    - "xform/^(.*)ú(.*)$/$1u$2②/"
    - "xform/^(.*)ǔ(.*)$/$1u$2③/"
    - "xform/^(.*)ù(.*)$/$1u$2④/"
    - "xform/^(.*)ǖ(.*)$/$1v$2①/"
    - "xform/^(.*)ǘ(.*)$/$1v$2②/"
    - "xform/^(.*)ǚ(.*)$/$1v$2③/"
    - "xform/^(.*)ǜ(.*)$/$1v$2④/"
    - "xform/^(.*)ü(.*)$/$1v$2/"
    - "xform/^(.*)ń(.*)$/$1n$2②/"
    - "xform/^(.*)ň(.*)$/$1n$2③/"
    - "xform/^(.*)ǹ(.*)$/$1n$2④/"
    - "xlit/①②③④/7890/"
    - "derive/^ng(\\d)$/eng$1/"
    - "xform/^n(\\d)/en$1/"
    - "derive/^([jqxy])u(\\d)$/$1v$2/"
    - "xform/^([aoe].*)(\\d)$/Ⓞ$1$2/"
    - "xform/en(\\d)$/Ⓦ$1/"
    - "xform/eng(\\d)$/Ⓣ$1/"
    - "xform/in|uai(\\d)$/Ⓨ$1/"
    - "xform/^zh/Ⓤ/"
    - "xform/^sh/Ⓘ/"
    - "xform/uo(\\d)$/Ⓞ$1/"
    - "xform/ai(\\d)$/Ⓟ$1/"
    - "xform/^ch/Ⓐ/"
    - "xform/[iu]ang(\\d)$/Ⓖ$1/"
    - "xform/ang(\\d)$/Ⓢ$1/"
    - "xform/ie(\\d)$/Ⓓ$1/"
    - "xform/ian(\\d)$/Ⓕ$1/"
    - "xform/(.)i?ong(\\d)$/$1Ⓗ$2/"
    - "xform/er|iu$/Ⓙ$1/"
    - "xform/ei(\\d)$/Ⓚ$1/"
    - "xform/uan(\\d)$/Ⓛ$1/"
    - "xform/ing(\\d)$/;$1/"
    - "xform/ou(\\d)$/Ⓩ$1/"
    - "xform/[iu]a(\\d)$/Ⓧ$1/"
    - "xform/iao(\\d)$/Ⓑ$1/"
    - "xform/ue|ui|ve(\\d)$/Ⓝ$1/"
    - "xform/un(\\d)$/Ⓜ$1/"
    - "xform/ao(\\d)$/Ⓠ$1/"
    - "xform/an(\\d)$/Ⓡ$1/"
    - "xlit/ⓌⓉⓎⓊⒾⓄⓅⒶⒼⓈⒹⒻⒽⒿⓀⓁⓏⓍⒷⓃⓂⓆⓇ/wtyuiopagsdfhjklzxbnmqr/"
    - "derive/^(..)(\\d)$/$1/"
    - "derive/^(.).+(\\d)$/$1$2/"
"自然码":
  __append:
    - "xform/^([a-z]+)$/$1④/"
    - "xform/^(.*)ā(.*)$/$1a$2①/"
    - "xform/^(.*)á(.*)$/$1a$2②/"
    - "xform/^(.*)ǎ(.*)$/$1a$2③/"
    - "xform/^(.*)à(.*)$/$1a$2④/"
    - "xform/^(.*)ō(.*)$/$1o$2①/"
    - "xform/^(.*)ó(.*)$/$1o$2②/"
    - "xform/^(.*)ǒ(.*)$/$1o$2③/"
    - "xform/^(.*)ò(.*)$/$1o$2④/"
    - "xform/^(.*)ē(.*)$/$1e$2①/"
    - "xform/^(.*)é(.*)$/$1e$2②/"
    - "xform/^(.*)ě(.*)$/$1e$2③/"
    - "xform/^(.*)è(.*)$/$1e$2④/"
    - "xform/^(.*)ī(.*)$/$1i$2①/"
    - "xform/^(.*)í(.*)$/$1i$2②/"
    - "xform/^(.*)ǐ(.*)$/$1i$2③/"
    - "xform/^(.*)ì(.*)$/$1i$2④/"
    - "xform/^(.*)ū(.*)$/$1u$2①/"
    - "xform/^(.*)ú(.*)$/$1u$2②/"
    - "xform/^(.*)ǔ(.*)$/$1u$2③/"
    - "xform/^(.*)ù(.*)$/$1u$2④/"
    - "xform/^(.*)ǖ(.*)$/$1v$2①/"
    - "xform/^(.*)ǘ(.*)$/$1v$2②/"
    - "xform/^(.*)ǚ(.*)$/$1v$2③/"
    - "xform/^(.*)ǜ(.*)$/$1v$2④/"
    - "xform/^(.*)ü(.*)$/$1v$2/"
    - "xform/^(.*)ń(.*)$/$1n$2②/"
    - "xform/^(.*)ň(.*)$/$1n$2③/"
    - "xform/^(.*)ǹ(.*)$/$1n$2④/"
    - "xlit/①②③④/7890/"
    - "derive/^ng(\\d)$/eng$1/"
    - "xform/^n(\\d)/en$1/"
    - "derive/^([jqxy])u(\\d)$/$1v$2/"
    - "derive/^([aoe])([ioun])(\\d)$/$1$1$2$3/"
    - "xform/^([aoe])(ng)?(\\d)$/$1$1$2$3/"
    - "xform/iu(\\d)$/Ⓠ$1/"
    - "xform/[iu]a(\\d)$/Ⓦ$1/"
    - "xform/[uv]an(\\d)$/Ⓡ$1/"
    - "xform/[uv]e(\\d)$/Ⓣ$1/"
    - "xform/ing|uai(\\d)$/Ⓨ$1/"
    - "xform/^sh/Ⓤ/"
    - "xform/^ch/Ⓘ/"
    - "xform/^zh/Ⓥ/"
    - "xform/uo(\\d)$/Ⓞ$1/"
    - "xform/[uv]n(\\d)$/Ⓟ$1/"
    - "xform/(.)i?ong(\\d)$/$1Ⓢ$2/"
    - "xform/[iu]ang(\\d)$/Ⓓ$1/"
    - "xform/(.)en(\\d)$/$1Ⓕ$2/"
    - "xform/(.)eng(\\d)$/$1Ⓖ$2/"
    - "xform/(.)ang(\\d)$/$1Ⓗ$2/"
    - "xform/ian(\\d)$/Ⓜ$1/"
    - "xform/(.)an(\\d)$/$1Ⓙ$2/"
    - "xform/iao(\\d)$/Ⓒ$1/"
    - "xform/(.)ao(\\d)$/$1Ⓚ$2/"
    - "xform/(.)ai(\\d)$/$1Ⓛ$2/"
    - "xform/(.)ei(\\d)$/$1Ⓩ$2/"
    - "xform/ie(\\d)$/Ⓧ$1/"
    - "xform/ui(\\d)$/Ⓥ$1/"
    - "xform/(.)ou(\\d)$/$1Ⓑ$2/"
    - "xform/in(\\d)$/Ⓝ$1/"
    - "xlit/ⓆⓌⓇⓉⓎⓊⒾⓄⓅⓈⒹⒻⒼⒽⓂⒿⒸⓀⓁⓏⓍⓋⒷⓃ/qwrtyuiopsdfghmjcklzxvbn/"
    - "xform/^(aj|ak|al|ob|ez|ef)(\\d?)$//"
    - "derive/^(..)(\\d)$/$1/"
    - "derive/^(.).+(\\d)$/$1$2/"
"自然龙":
  __append:
    - "xform/^(ā|á|ǎ|à)([ioun])$/a$1$2/"
    - "xform/^(ō|ó|ǒ|ò)([ioun])$/o$1$2/"
    - "xform/^(ē|é|ě|è)([ioun])$/e$1$2/"
    - "xform/^(ā|á|ǎ|à)(ng)$/a$1$2/"
    - "xform/^(ō|ó|ǒ|ò)(ng)$/o$1$2/"
    - "xform/^(ē|é|ě|è)(ng)$/e$1$2/"
    - "xform/^(ā|á|ǎ|à)$/a$1/"
    - "xform/^(ō|ó|ǒ|ò)$/o$1/"
    - "xform/^(ē|é|ě|è)$/e$1/"
    - "xform/^([jqxy])u$/$1ü/"
    - "xform/^([jqxy])ū$/$1ǖ/"
    - "xform/^([jqxy])ú$/$1ǘ/"
    - "xform/^([jqxy])ǔ$/$1ǚ/"
    - "xform/^([jqxy])ù$/$1ǜ/"
    - "xform/^ǹg$/eèng/"
    - "xform/^ňg$/eěng/"
    - "xform/^ńg$/eéng/"
    - "xform/^ng$/eeng/"
    - "xform/^ǹ$/eèn/"
    - "xform/^ň$/eěn/"
    - "xform/^ń$/eén/"
    - "xform/^n$/een/"
    - "xform/^ēr$/eQ/"
    - "xform/^ér$/eK/"
    - "xform/^ěr$/eU/"
    - "xform/^èr$/eH/"
    - "xform/^er$/eQ/"
    - "xform/^a$/aā/"
    - "xform/^o$/oō/"
    - "xform/^e$/eē/"
    - "xform/^sh/U/"
    - "xform/^ch/I/"
    - "xform/^zh/V/"
    - "xform/^(.)(iáo|iǎng|uǎng|ang|āng|ue|uē|üe|ǖe|ǎi|á)$/$1U/"
    - "xform/^(.)(iàng|iǒng|uàng|ēn|en|īng|ing|é|ó)$/$1E/"
    - "xform/^(.)(iǎn|iōng|iong|uǎi|uò|ǎng|ō|o)$/$1P/"
    - "xform/^(.)(uāng|uang|ong|ǐng|ìng|uí|áng)$/$1W/"
    - "xform/^(.)(uǎn|uái||uā|ua|én|uō|uo|ié|ǚ)$/$1S/"
    - "xform/^(.)(uán|ài|ěn|èn|uě|ǚe|ǎn|ǔn|iù)$/$1O/"
    - "xform/^(.)(uān|uan|àng|ái|iā|ia|uè|üè)$/$1D/"
    - "xform/^(.)(iáng|áo|ué|üé|ēi|ei|à|è|ǒ)$/$1I/"
    - "xform/^(.)(uāi|uai|uà|uǎ|ūn|un|ò|ǐ)$/$1G/"
    - "xform/^(.)(éng|èng|uài|èi|uì|ǜ|ún)$/$1F/"
    - "xform/^(.)(ióng|ōng|ong|án|iē|ie)$/$1K/"
    - "xform/^(.)(iào|iǎo|uǒ|uó|a|ā|ě|ú)$/$1L/"
    - "xform/^(.)(uàn|ēng|eng|iá|ín|iě)$/$1C/"
    - "xform/^(.)(iān|ian|òu|éi|ùn|ē|e)$/$1R/"
    - "xform/^(.)(iāng|iang|ěng|òng)$/$1Y/"
    - "xform/^(.)(iao|iāo|ǔ|ǎ|iú|ǘ)$/$1M/"
    - "xform/^(.)(iǎ|íng|ān|an|ǒng)$/$1N/"
    - "xform/^(.)(iòng|īn|in|ǖ|ù)$/$1H/"
    - "xform/^(.)(ǎo|ià|ǐn|ōu|ou)$/$1X/"
    - "xform/^(.)(óng|àn|ěi|ī|i)$/$1J/"
    - "xform/^(.)(ián|ào|ìn|uǐ)$/$1V/"
    - "xform/^(.)(uáng|āi|ai|í)$/$1B/"
    - "xform/^(.)(ǒu|iū|iǔ|iu)$/$1Z/"
    - "xform/^(.)(uá|uī|ui|ì)$/$1T/"
    - "xform/^(.)(ū|u|óu|iàn)$/$1A/"
    - "xform/^(.)(āo|ao|iè)$/$1Q/"
    - "xlit/QWERTYUIOPASDFGHMJCKLZXVBN/qwertyuiopasdfghmjcklzxvbn/"