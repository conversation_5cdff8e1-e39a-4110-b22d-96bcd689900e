# Rime default settings
# encoding: utf-8

patch:
  ascii_composer:
    good_old_caps_lock: true  # true | false
    switch_key:
      Caps_Lock: commit_code      # commit_code | commit_text | clear
      Shift_L: noop  # commit_code | commit_text | inline_ascii | clear | noop
      Shift_R: commit_code         # commit_code | commit_text | inline_ascii | clear | noop
      Control_L: noop       # commit_code | commit_text | inline_ascii | clear | noop
      Control_R: noop       # commit_code | commit_text | inline_ascii | clear | noop

  recognizer:
    patterns:
      email: "^[A-Za-z][-_.0-9A-Za-z]*@.*$"                            # email @ 之后不上屏
      url: "^(www[.]|https?:|ftp[.:]|mailto:|file:).*$|^[a-z]+[.].+$"  # URL
      underscore: "^[A-Za-z]+_.*"  # 下划线不上屏
      url_2: "^[A-Za-z]+[.].*"   # 句号不上屏，支持 google.com abc.txt 等网址或文件名，使用句号翻页时需要注释掉
      colon: "^[A-Za-z]+:.*"     # 冒号不上屏
